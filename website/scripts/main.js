// Interview Prep Application
class InterviewPrepApp {
    constructor() {
        this.exercises = [];
        this.currentExercise = null;
        this.completedExercises = new Set();
        this.currentFilter = 'all';
        this.currentCategory = 'all';
        
        this.init();
    }

    async init() {
        await this.loadExercises();
        this.loadProgress();
        this.setupEventListeners();
        this.renderExerciseList();
        this.updateStats();
        this.updateProgress();
    }

    async loadExercises() {
        try {
            const response = await fetch('data/exercises.json');
            const data = await response.json();
            this.exercises = data.exercises.sort((a, b) => {
                // Sort by difficulty first (Easy, Medium, Hard), then by ID
                const difficultyOrder = { 'Easy': 1, 'Medium': 2, 'Hard': 3 };
                if (difficultyOrder[a.difficulty] !== difficultyOrder[b.difficulty]) {
                    return difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty];
                }
                return a.id - b.id;
            });
        } catch (error) {
            console.error('Failed to load exercises:', error);
            this.exercises = [];
        }
    }

    loadProgress() {
        const saved = localStorage.getItem('interviewPrepProgress');
        if (saved) {
            this.completedExercises = new Set(JSON.parse(saved));
        }
    }

    saveProgress() {
        localStorage.setItem('interviewPrepProgress', JSON.stringify([...this.completedExercises]));
    }

    setupEventListeners() {
        // Difficulty filter buttons
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentFilter = e.target.dataset.difficulty;
                this.renderExerciseList();
            });
        });

        // Category filter
        document.getElementById('category-select').addEventListener('change', (e) => {
            this.currentCategory = e.target.value;
            this.renderExerciseList();
        });

        // Learning path buttons
        document.querySelectorAll('.path-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const pathCard = e.target.closest('.path-card');
                const path = pathCard.dataset.path;
                this.startLearningPath(path);
            });
        });

        // Navigation buttons
        document.getElementById('prev-btn').addEventListener('click', () => this.navigateExercise(-1));
        document.getElementById('next-btn').addEventListener('click', () => this.navigateExercise(1));

        // Mark complete button
        document.getElementById('mark-complete-btn').addEventListener('click', () => this.toggleComplete());

        // View code button
        document.getElementById('view-code-btn').addEventListener('click', () => this.showCodeModal());

        // Modal close and copy
        document.getElementById('close-modal').addEventListener('click', () => this.closeModal());
        document.getElementById('copy-code-btn').addEventListener('click', () => this.copyCode());
        document.getElementById('code-modal').addEventListener('click', (e) => {
            if (e.target.id === 'code-modal') this.closeModal();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') this.closeModal();
            if (e.key === 'ArrowLeft' && this.currentExercise) this.navigateExercise(-1);
            if (e.key === 'ArrowRight' && this.currentExercise) this.navigateExercise(1);
        });
    }

    renderExerciseList() {
        const container = document.getElementById('exercise-list');
        const filteredExercises = this.exercises.filter(exercise => {
            const matchesDifficulty = this.currentFilter === 'all' || exercise.difficulty === this.currentFilter;
            const matchesCategory = this.currentCategory === 'all' || exercise.category === this.currentCategory;
            return matchesDifficulty && matchesCategory;
        });

        container.innerHTML = filteredExercises.map(exercise => `
            <div class="exercise-item ${this.currentExercise?.id === exercise.id ? 'active' : ''} ${this.completedExercises.has(exercise.id) ? 'completed' : ''}" 
                 data-id="${exercise.id}">
                <div class="exercise-item-header">
                    <div class="exercise-item-title">${exercise.title}</div>
                    <div class="exercise-item-number">#${exercise.leetcodeNumber}</div>
                </div>
                <div class="exercise-item-meta">
                    <span class="difficulty-tag ${exercise.difficulty.toLowerCase()}">${exercise.difficulty}</span>
                    <span class="category-tag">${exercise.category}</span>
                    ${this.completedExercises.has(exercise.id) ? '<span class="completion-status"><i class="fas fa-check"></i></span>' : ''}
                </div>
            </div>
        `).join('');

        // Add click listeners to exercise items
        container.querySelectorAll('.exercise-item').forEach(item => {
            item.addEventListener('click', () => {
                const exerciseId = parseInt(item.dataset.id);
                this.showExercise(exerciseId);
            });
        });
    }

    showExercise(exerciseId) {
        const exercise = this.exercises.find(ex => ex.id === exerciseId);
        if (!exercise) return;

        this.currentExercise = exercise;
        
        // Hide welcome screen, show exercise detail
        document.getElementById('welcome-screen').style.display = 'none';
        document.getElementById('exercise-detail').style.display = 'block';

        // Update exercise details
        document.getElementById('exercise-title').textContent = exercise.title;
        document.getElementById('exercise-difficulty').textContent = exercise.difficulty;
        document.getElementById('exercise-difficulty').className = `difficulty-badge ${exercise.difficulty.toLowerCase()}`;
        document.getElementById('exercise-category').textContent = exercise.category;
        document.getElementById('leetcode-number').textContent = exercise.leetcodeNumber;
        document.getElementById('leetcode-link').href = `https://leetcode.com/problems/${this.slugify(exercise.title)}/`;
        document.getElementById('exercise-description').textContent = exercise.description;
        document.getElementById('time-complexity').textContent = exercise.timeComplexity;
        document.getElementById('space-complexity').textContent = exercise.spaceComplexity;

        // Update patterns
        const patternsContainer = document.getElementById('patterns-list');
        patternsContainer.innerHTML = exercise.patterns.map(pattern => 
            `<span class="pattern-tag">${pattern}</span>`
        ).join('');

        // Update completion button
        const completeBtn = document.getElementById('mark-complete-btn');
        const isCompleted = this.completedExercises.has(exercise.id);
        completeBtn.innerHTML = isCompleted ? 
            '<i class="fas fa-check"></i> Completed' : 
            '<i class="fas fa-check"></i> Mark Complete';
        completeBtn.className = isCompleted ? 'action-btn completed' : 'action-btn';

        // Update navigation buttons
        const currentIndex = this.exercises.findIndex(ex => ex.id === exerciseId);
        document.getElementById('prev-btn').disabled = currentIndex === 0;
        document.getElementById('next-btn').disabled = currentIndex === this.exercises.length - 1;

        // Update exercise list highlighting
        this.renderExerciseList();
    }

    navigateExercise(direction) {
        if (!this.currentExercise) return;
        
        const currentIndex = this.exercises.findIndex(ex => ex.id === this.currentExercise.id);
        const newIndex = currentIndex + direction;
        
        if (newIndex >= 0 && newIndex < this.exercises.length) {
            this.showExercise(this.exercises[newIndex].id);
        }
    }

    toggleComplete() {
        if (!this.currentExercise) return;

        if (this.completedExercises.has(this.currentExercise.id)) {
            this.completedExercises.delete(this.currentExercise.id);
        } else {
            this.completedExercises.add(this.currentExercise.id);
        }

        this.saveProgress();
        this.updateStats();
        this.updateProgress();
        this.showExercise(this.currentExercise.id); // Refresh the view
    }

    async showCodeModal() {
        if (!this.currentExercise) return;

        try {
            // Show loading state
            document.getElementById('code-content').textContent = 'Loading code...';
            document.getElementById('code-modal').classList.add('active');

            // Use the original file path - the server will handle routing
            const filePath = this.currentExercise.filePath;

            const response = await fetch(filePath);
            if (!response.ok) {
                throw new Error(`Failed to load file: ${response.status} ${response.statusText}`);
            }

            const codeContent = await response.text();
            const codeElement = document.getElementById('code-content');
            codeElement.textContent = codeContent;

            // Apply syntax highlighting if Prism is available
            if (typeof Prism !== 'undefined') {
                Prism.highlightElement(codeElement);
            }
        } catch (error) {
            console.error('Failed to load code:', error);

            // Show error message with fallback content
            const errorContent = `// Error loading code file: ${this.currentExercise.filePath}
// ${error.message}
//
// ${this.currentExercise.title} - LeetCode ${this.currentExercise.leetcodeNumber}
//
// Please ensure the file exists at: ${this.currentExercise.filePath}
//
// Expected solution structure:

package algorithms.augment.${this.currentExercise.category.toLowerCase().replace(' ', '')};

/**
 * ${this.currentExercise.title} - LeetCode ${this.currentExercise.leetcodeNumber}
 * ${this.currentExercise.description}
 */
public class ${this.currentExercise.title.replace(/[^a-zA-Z0-9]/g, '')} {

    // Solution implementation would be here

    public static void main(String[] args) {
        // Test cases would be here
    }
}`;

            const codeElement = document.getElementById('code-content');
            codeElement.textContent = errorContent;

            // Apply syntax highlighting if Prism is available
            if (typeof Prism !== 'undefined') {
                Prism.highlightElement(codeElement);
            }
        }
    }

    closeModal() {
        document.getElementById('code-modal').classList.remove('active');
    }

    async copyCode() {
        try {
            const codeContent = document.getElementById('code-content').textContent;
            await navigator.clipboard.writeText(codeContent);

            // Show visual feedback
            const copyBtn = document.getElementById('copy-code-btn');
            const originalHTML = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i>';
            copyBtn.classList.add('copied');

            setTimeout(() => {
                copyBtn.innerHTML = originalHTML;
                copyBtn.classList.remove('copied');
            }, 2000);

        } catch (error) {
            console.error('Failed to copy code:', error);

            // Fallback for older browsers
            const codeElement = document.getElementById('code-content');
            const range = document.createRange();
            range.selectNode(codeElement);
            window.getSelection().removeAllRanges();
            window.getSelection().addRange(range);

            try {
                document.execCommand('copy');
                window.getSelection().removeAllRanges();
            } catch (fallbackError) {
                console.error('Fallback copy also failed:', fallbackError);
            }
        }
    }

    startLearningPath(path) {
        let filteredExercises = [];
        
        switch (path) {
            case 'beginner':
                filteredExercises = this.exercises.filter(ex => ex.difficulty === 'Easy');
                this.currentFilter = 'Easy';
                document.querySelector('[data-difficulty="Easy"]').click();
                break;
            case 'intermediate':
                filteredExercises = this.exercises;
                this.currentFilter = 'all';
                document.querySelector('[data-difficulty="all"]').click();
                break;
            case 'advanced':
                filteredExercises = this.exercises.filter(ex => ex.difficulty === 'Hard');
                this.currentFilter = 'Hard';
                document.querySelector('[data-difficulty="Hard"]').click();
                break;
        }

        if (filteredExercises.length > 0) {
            this.showExercise(filteredExercises[0].id);
        }
    }

    updateStats() {
        const totalExercises = this.exercises.length;
        const completedCount = this.completedExercises.size;
        const easyCount = this.exercises.filter(ex => ex.difficulty === 'Easy').length;
        const mediumCount = this.exercises.filter(ex => ex.difficulty === 'Medium').length;
        const hardCount = this.exercises.filter(ex => ex.difficulty === 'Hard').length;

        document.getElementById('total-exercises').textContent = totalExercises;
        document.getElementById('completed-exercises').textContent = completedCount;
        document.getElementById('easy-count').textContent = easyCount;
        document.getElementById('medium-count').textContent = mediumCount;
        document.getElementById('hard-count').textContent = hardCount;
    }

    updateProgress() {
        const totalExercises = this.exercises.length;
        const completedCount = this.completedExercises.size;
        const percentage = totalExercises > 0 ? (completedCount / totalExercises) * 100 : 0;

        document.getElementById('overall-progress').style.width = `${percentage}%`;
        document.getElementById('progress-text').textContent = `${completedCount}/${totalExercises}`;
    }

    slugify(text) {
        return text.toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/[\s_-]+/g, '-')
            .replace(/^-+|-+$/g, '');
    }
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new InterviewPrepApp();
});
