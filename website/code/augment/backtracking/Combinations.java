package algorithms.augment.backtracking;

import java.util.*;

/**
 * Combinations - LeetCode 77
 * Given two integers n and k, return all possible combinations of k numbers out of the range [1, n].
 */
public class Combinations {
    
    public List<List<Integer>> combine(int n, int k) {
        List<List<Integer>> result = new ArrayList<>();
        backtrack(result, new ArrayList<>(), 1, n, k);
        return result;
    }
    
    private void backtrack(List<List<Integer>> result, List<Integer> current, 
                          int start, int n, int k) {
        if (current.size() == k) {
            result.add(new ArrayList<>(current));
            return;
        }
        
        for (int i = start; i <= n; i++) {
            current.add(i);
            backtrack(result, current, i + 1, n, k);
            current.remove(current.size() - 1);
        }
    }
    
    public static void main(String[] args) {
        Combinations solution = new Combinations();
        
        List<List<Integer>> result = solution.combine(4, 2);
        System.out.println("Combinations of 4 choose 2: " + result);
        // Expected: [[1,2],[1,3],[1,4],[2,3],[2,4],[3,4]]
    }
}
