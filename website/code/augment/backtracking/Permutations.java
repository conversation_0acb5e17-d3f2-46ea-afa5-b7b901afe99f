package algorithms.augment.backtracking;

import java.util.*;

/**
 * Permutations - LeetCode 46
 * Given an array nums of distinct integers, return all the possible permutations.
 */
public class Permutations {
    
    public List<List<Integer>> permute(int[] nums) {
        List<List<Integer>> result = new ArrayList<>();
        backtrack(result, new ArrayList<>(), nums);
        return result;
    }
    
    private void backtrack(List<List<Integer>> result, List<Integer> current, int[] nums) {
        if (current.size() == nums.length) {
            result.add(new ArrayList<>(current));
            return;
        }
        
        for (int num : nums) {
            if (current.contains(num)) continue; // Skip if already used
            
            current.add(num);
            backtrack(result, current, nums);
            current.remove(current.size() - 1); // Backtrack
        }
    }
    
    // More efficient version using boolean array
    public List<List<Integer>> permuteOptimal(int[] nums) {
        List<List<Integer>> result = new ArrayList<>();
        boolean[] used = new boolean[nums.length];
        backtrackOptimal(result, new ArrayList<>(), nums, used);
        return result;
    }
    
    private void backtrackOptimal(List<List<Integer>> result, List<Integer> current, 
                                  int[] nums, boolean[] used) {
        if (current.size() == nums.length) {
            result.add(new ArrayList<>(current));
            return;
        }
        
        for (int i = 0; i < nums.length; i++) {
            if (used[i]) continue;
            
            current.add(nums[i]);
            used[i] = true;
            backtrackOptimal(result, current, nums, used);
            used[i] = false;
            current.remove(current.size() - 1);
        }
    }
    
    public static void main(String[] args) {
        Permutations solution = new Permutations();
        
        int[] nums = {1, 2, 3};
        List<List<Integer>> result = solution.permute(nums);
        System.out.println("Permutations: " + result);
        // Expected: [[1,2,3],[1,3,2],[2,1,3],[2,3,1],[3,1,2],[3,2,1]]
    }
}
