package algorithms.augment.arrays;

import java.util.*;

/**
 * Top K Frequent Elements - LeetCode 347
 * Given an integer array nums and an integer k, return the k most frequent elements.
 */
public class TopKFrequentElements {
    
    public int[] topKFrequent(int[] nums, int k) {
        Map<Integer, Integer> count = new HashMap<>();
        for (int num : nums) {
            count.put(num, count.getOrDefault(num, 0) + 1);
        }
        
        PriorityQueue<Integer> heap = new PriorityQueue<>((a, b) -> count.get(b) - count.get(a));
        heap.addAll(count.keySet());
        
        int[] result = new int[k];
        for (int i = 0; i < k; i++) {
            result[i] = heap.poll();
        }
        
        return result;
    }
    
    public static void main(String[] args) {
        TopKFrequentElements solution = new TopKFrequentElements();
        int[] nums = {1, 1, 1, 2, 2, 3};
        System.out.println(Arrays.toString(solution.topKFrequent(nums, 2))); // [1, 2]
    }
}
