package algorithms.augment.math;

import java.util.*;

/**
 * Happy Number - LeetCode 202
 * Write an algorithm to determine if a number n is happy.
 * A happy number is a number defined by the following process:
 * - Starting with any positive integer, replace the number by the sum of the squares of its digits.
 * - Repeat the process until the number equals 1 (where it will stay), or it loops endlessly in a cycle.
 * - Those numbers for which this process ends in 1 are happy.
 */
public class HappyNumber {
    
    public boolean isHappy(int n) {
        Set<Integer> seen = new HashSet<>();
        
        while (n != 1 && !seen.contains(n)) {
            seen.add(n);
            n = getNext(n);
        }
        
        return n == 1;
    }
    
    // <PERSON>'s Cycle Detection (Two Pointers)
    public boolean isHappyOptimal(int n) {
        int slow = n;
        int fast = getNext(n);
        
        while (fast != 1 && slow != fast) {
            slow = getNext(slow);
            fast = getNext(getNext(fast));
        }
        
        return fast == 1;
    }
    
    private int getNext(int n) {
        int totalSum = 0;
        while (n > 0) {
            int digit = n % 10;
            n = n / 10;
            totalSum += digit * digit;
        }
        return totalSum;
    }
    
    public static void main(String[] args) {
        HappyNumber solution = new HappyNumber();
        
        System.out.println("19 is happy: " + solution.isHappy(19)); // true
        System.out.println("2 is happy: " + solution.isHappy(2));   // false
        System.out.println("7 is happy: " + solution.isHappy(7));   // true
    }
}
