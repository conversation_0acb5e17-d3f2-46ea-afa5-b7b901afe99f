package algorithms.augment.search;

/**
 * Search in Rotated Sorted Array - LeetCode 33
 * There is an integer array nums sorted in ascending order (with distinct values).
 * Prior to being passed to your function, nums is possibly rotated at an unknown pivot index k.
 * Given the array nums after the possible rotation and an integer target, return the index of target if it is in nums, or -1 if it is not in nums.
 */
public class SearchInRotatedArray {
    
    public int search(int[] nums, int target) {
        int left = 0, right = nums.length - 1;
        
        while (left <= right) {
            int mid = left + (right - left) / 2;
            
            if (nums[mid] == target) {
                return mid;
            }
            
            // Check if left half is sorted
            if (nums[left] <= nums[mid]) {
                // Target is in the left sorted half
                if (nums[left] <= target && target < nums[mid]) {
                    right = mid - 1;
                } else {
                    left = mid + 1;
                }
            } else {
                // Right half is sorted
                // Target is in the right sorted half
                if (nums[mid] < target && target <= nums[right]) {
                    left = mid + 1;
                } else {
                    right = mid - 1;
                }
            }
        }
        
        return -1;
    }
    
    public static void main(String[] args) {
        SearchInRotatedArray solution = new SearchInRotatedArray();
        
        int[] nums1 = {4, 5, 6, 7, 0, 1, 2};
        System.out.println("Index of 0: " + solution.search(nums1, 0)); // 4
        System.out.println("Index of 3: " + solution.search(nums1, 3)); // -1
        
        int[] nums2 = {1};
        System.out.println("Index of 0: " + solution.search(nums2, 0)); // -1
    }
}
