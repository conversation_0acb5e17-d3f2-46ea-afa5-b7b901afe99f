package algorithms.augment.search;

/**
 * Find Minimum in Rotated Sorted Array - LeetCode 153
 * Suppose an array of length n sorted in ascending order is rotated between 1 and n times.
 * Given the sorted rotated array nums of unique elements, return the minimum element of this array.
 */
public class FindMinInRotatedArray {
    
    public int findMin(int[] nums) {
        int left = 0, right = nums.length - 1;
        
        while (left < right) {
            int mid = left + (right - left) / 2;
            
            // If mid element is greater than right element,
            // minimum is in the right half
            if (nums[mid] > nums[right]) {
                left = mid + 1;
            } else {
                // Minimum is in the left half (including mid)
                right = mid;
            }
        }
        
        return nums[left];
    }
    
    public static void main(String[] args) {
        FindMinInRotatedArray solution = new FindMinInRotatedArray();
        
        int[] nums1 = {3, 4, 5, 1, 2};
        System.out.println("Minimum: " + solution.findMin(nums1)); // 1
        
        int[] nums2 = {4, 5, 6, 7, 0, 1, 2};
        System.out.println("Minimum: " + solution.findMin(nums2)); // 0
        
        int[] nums3 = {11, 13, 15, 17};
        System.out.println("Minimum: " + solution.findMin(nums3)); // 11
    }
}
