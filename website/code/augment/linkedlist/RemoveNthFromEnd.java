package algorithms.augment.linkedlist;

/**
 * Remove Nth Node From End of List - LeetCode 19
 * Given the head of a linked list, remove the nth node from the end of the list and return its head.
 */
public class RemoveNthFromEnd {
    
    public ListNode removeNthFromEnd(ListNode head, int n) {
        ListNode dummy = new ListNode(0);
        dummy.next = head;
        
        ListNode first = dummy;
        ListNode second = dummy;
        
        // Move first pointer n+1 steps ahead
        for (int i = 0; i <= n; i++) {
            first = first.next;
        }
        
        // Move both pointers until first reaches the end
        while (first != null) {
            first = first.next;
            second = second.next;
        }
        
        // Remove the nth node from end
        second.next = second.next.next;
        
        return dummy.next;
    }
    
    public static void main(String[] args) {
        RemoveNthFromEnd solution = new RemoveNthFromEnd();
        
        ListNode head = ListNode.createList(new int[]{1, 2, 3, 4, 5});
        System.out.print("Original: ");
        ListNode.printList(head);
        
        ListNode result = solution.removeNthFromEnd(head, 2);
        System.out.print("After removing 2nd from end: ");
        ListNode.printList(result);
    }
}
