package algorithms.augment.linkedlist;

/**
 * Reorder List - LeetCode 143
 * You are given the head of a singly linked-list. The list can be represented as:
 * L0 → L1 → … → Ln - 1 → Ln
 * Reorder the list to be on the following form:
 * L0 → Ln → L1 → Ln - 1 → L2 → Ln - 2 → …
 */
public class ReorderList {
    
    public void reorderList(ListNode head) {
        if (head == null || head.next == null) return;
        
        // Find the middle of the list
        ListNode slow = head, fast = head;
        while (fast.next != null && fast.next.next != null) {
            slow = slow.next;
            fast = fast.next.next;
        }
        
        // Reverse the second half
        ListNode secondHalf = reverseList(slow.next);
        slow.next = null;
        
        // Merge the two halves
        ListNode first = head, second = secondHalf;
        while (second != null) {
            ListNode temp1 = first.next;
            ListNode temp2 = second.next;
            
            first.next = second;
            second.next = temp1;
            
            first = temp1;
            second = temp2;
        }
    }
    
    private ListNode reverseList(ListNode head) {
        ListNode prev = null;
        while (head != null) {
            ListNode next = head.next;
            head.next = prev;
            prev = head;
            head = next;
        }
        return prev;
    }
    
    public static void main(String[] args) {
        ReorderList solution = new ReorderList();
        
        ListNode head = ListNode.createList(new int[]{1, 2, 3, 4});
        System.out.print("Original: ");
        ListNode.printList(head);
        
        solution.reorderList(head);
        System.out.print("Reordered: ");
        ListNode.printList(head);
    }
}
