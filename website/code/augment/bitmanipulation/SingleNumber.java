package algorithms.augment.bitmanipulation;

/**
 * Single Number - LeetCode 136
 * Given a non-empty array of integers nums, every element appears twice except for one. 
 * Find that single one.
 */
public class SingleNumber {
    
    // XOR approach - O(n) time, O(1) space
    public int singleNumber(int[] nums) {
        int result = 0;
        for (int num : nums) {
            result ^= num; // XOR operation
        }
        return result;
    }
    
    // Mathematical approach - O(n) time, O(n) space
    public int singleNumberMath(int[] nums) {
        java.util.Set<Integer> set = new java.util.HashSet<>();
        int sumOfSet = 0, sumOfNums = 0;
        
        for (int num : nums) {
            if (!set.contains(num)) {
                set.add(num);
                sumOfSet += num;
            }
            sumOfNums += num;
        }
        
        return 2 * sumOfSet - sumOfNums;
    }
    
    public static void main(String[] args) {
        SingleNumber solution = new SingleNumber();
        
        int[] nums1 = {2, 2, 1};
        System.out.println("Single number: " + solution.singleNumber(nums1)); // 1
        
        int[] nums2 = {4, 1, 2, 1, 2};
        System.out.println("Single number: " + solution.singleNumber(nums2)); // 4
        
        int[] nums3 = {1};
        System.out.println("Single number: " + solution.singleNumber(nums3)); // 1
    }
}
