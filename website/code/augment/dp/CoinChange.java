package algorithms.augment.dp;

import java.util.Arrays;

/**
 * Coin Change - LeetCode 322
 * You are given an integer array coins representing coins of different denominations
 * and an integer amount representing a total amount of money.
 * Return the fewest number of coins that you need to make up that amount.
 * If that amount of money cannot be made up by any combination of the coins, return -1.
 */
public class CoinChange {
    
    public int coinChange(int[] coins, int amount) {
        int[] dp = new int[amount + 1];
        Arrays.fill(dp, amount + 1); // Initialize with impossible value
        dp[0] = 0; // Base case: 0 coins needed for amount 0
        
        for (int i = 1; i <= amount; i++) {
            for (int coin : coins) {
                if (coin <= i) {
                    dp[i] = Math.min(dp[i], dp[i - coin] + 1);
                }
            }
        }
        
        return dp[amount] > amount ? -1 : dp[amount];
    }
    
    public static void main(String[] args) {
        CoinChange solution = new CoinChange();
        
        int[] coins1 = {1, 3, 4};
        System.out.println("Min coins for amount 6: " + solution.coinChange(coins1, 6)); // 2
        
        int[] coins2 = {2};
        System.out.println("Min coins for amount 3: " + solution.coinChange(coins2, 3)); // -1
        
        int[] coins3 = {1};
        System.out.println("Min coins for amount 0: " + solution.coinChange(coins3, 0)); // 0
    }
}
