package algorithms.augment.dp;

/**
 * Climbing Stairs - LeetCode 70
 * You are climbing a staircase. It takes n steps to reach the top.
 * Each time you can either climb 1 or 2 steps. In how many distinct ways can you climb to the top?
 */
public class ClimbingStairs {
    
    // Dynamic Programming approach - O(n) time, O(1) space
    public int climbStairs(int n) {
        if (n <= 2) {
            return n;
        }
        
        int prev2 = 1; // ways to reach step 1
        int prev1 = 2; // ways to reach step 2
        
        for (int i = 3; i <= n; i++) {
            int current = prev1 + prev2;
            prev2 = prev1;
            prev1 = current;
        }
        
        return prev1;
    }
    
    // Memoization approach - O(n) time, O(n) space
    public int climbStairsMemo(int n) {
        int[] memo = new int[n + 1];
        return climbStairsHelper(n, memo);
    }
    
    private int climbStairsHelper(int n, int[] memo) {
        if (n <= 2) {
            return n;
        }
        
        if (memo[n] != 0) {
            return memo[n];
        }
        
        memo[n] = climbStairsHelper(n - 1, memo) + climbStairsHelper(n - 2, memo);
        return memo[n];
    }
    
    public static void main(String[] args) {
        ClimbingStairs solution = new ClimbingStairs();
        
        System.out.println("Ways to climb 2 steps: " + solution.climbStairs(2)); // 2
        System.out.println("Ways to climb 3 steps: " + solution.climbStairs(3)); // 3
        System.out.println("Ways to climb 5 steps: " + solution.climbStairs(5)); // 8
    }
}
