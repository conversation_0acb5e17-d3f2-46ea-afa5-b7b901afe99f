/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo i {
    font-size: 1.5rem;
    color: #667eea;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
}

.progress-summary {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.progress-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #4a5568;
}

.progress-bar {
    width: 120px;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-size: 0.875rem;
    font-weight: 600;
    color: #2d3748;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    gap: 2rem;
}

/* Sidebar */
.sidebar {
    width: 350px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1.5rem;
    height: fit-content;
    position: sticky;
    top: 120px;
}

.learning-path h2 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1.5rem;
}

.learning-path h2 i {
    color: #667eea;
}

/* Filters */
.difficulty-filter {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.filter-btn {
    flex: 1;
    padding: 0.5rem 0.75rem;
    border: 1px solid #e2e8f0;
    background: white;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-btn:hover {
    border-color: #667eea;
    color: #667eea;
}

.filter-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.category-filter {
    margin-bottom: 1.5rem;
}

#category-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    font-size: 0.875rem;
    cursor: pointer;
}

/* Exercise List */
.exercise-list {
    max-height: 500px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.exercise-item {
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
}

.exercise-item:hover {
    border-color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.exercise-item.active {
    border-color: #667eea;
    background: #f7fafc;
}

.exercise-item.completed {
    background: #f0fff4;
    border-color: #68d391;
}

.exercise-item-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.exercise-item-title {
    font-weight: 600;
    color: #2d3748;
    font-size: 0.875rem;
    line-height: 1.4;
    flex: 1;
}

.exercise-item-number {
    font-size: 0.75rem;
    color: #718096;
    font-weight: 500;
    margin-left: 0.5rem;
}

.exercise-item-meta {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.difficulty-tag {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.difficulty-tag.easy {
    background: #c6f6d5;
    color: #22543d;
}

.difficulty-tag.medium {
    background: #fed7aa;
    color: #9c4221;
}

.difficulty-tag.hard {
    background: #fed7d7;
    color: #742a2a;
}

.category-tag {
    padding: 0.25rem 0.5rem;
    background: #edf2f7;
    color: #4a5568;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.completion-status {
    margin-left: auto;
    color: #68d391;
    font-size: 0.875rem;
}

/* Content Area */
.content-area {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 2rem;
}

/* Welcome Screen */
.welcome-screen {
    text-align: center;
}

.welcome-content h2 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
}

.welcome-content h2 i {
    color: #667eea;
}

.welcome-content p {
    font-size: 1.125rem;
    color: #4a5568;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    color: #718096;
    font-weight: 500;
}

.learning-paths h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1.5rem;
}

.path-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    max-width: 900px;
    margin: 0 auto;
}

.path-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.path-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    border-color: #667eea;
}

.path-card i {
    font-size: 2.5rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.path-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.75rem;
}

.path-card p {
    color: #718096;
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

.path-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.path-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

/* Exercise Detail View */
.exercise-detail {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.exercise-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.exercise-title-section h2 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
}

.exercise-meta {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.difficulty-badge, .category-badge {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
}

.difficulty-badge.easy {
    background: #c6f6d5;
    color: #22543d;
}

.difficulty-badge.medium {
    background: #fed7aa;
    color: #9c4221;
}

.difficulty-badge.hard {
    background: #fed7d7;
    color: #742a2a;
}

.category-badge {
    background: #edf2f7;
    color: #4a5568;
}

.leetcode-link a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
}

.leetcode-link a:hover {
    color: #5a67d8;
}

.exercise-actions {
    display: flex;
    gap: 0.75rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #667eea;
    color: white;
}

.action-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.action-btn.secondary {
    background: #edf2f7;
    color: #4a5568;
}

.action-btn.secondary:hover {
    background: #e2e8f0;
}

.action-btn.completed {
    background: #48bb78;
}

.action-btn.completed:hover {
    background: #38a169;
}

/* Exercise Content */
.exercise-content {
    space-y: 2rem;
}

.problem-description {
    margin-bottom: 2rem;
}

.problem-description h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1rem;
}

.problem-description p {
    line-height: 1.6;
    color: #4a5568;
    font-size: 1rem;
}

.problem-details {
    background: #f7fafc;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.detail-item h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.detail-item span {
    font-family: 'Monaco', 'Menlo', monospace;
    background: #667eea;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
}

#patterns-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.pattern-tag {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.navigation-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: 1px solid #e2e8f0;
    background: white;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #4a5568;
}

.nav-btn:hover:not(:disabled) {
    border-color: #667eea;
    color: #667eea;
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.nav-btn.primary {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.nav-btn.primary:hover {
    background: #5a67d8;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: white;
    border-radius: 12px;
    max-width: 90vw;
    max-height: 90vh;
    width: 800px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: #718096;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #f7fafc;
    color: #2d3748;
}

.modal-body {
    padding: 1.5rem;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-body pre {
    background: #1a202c;
    color: #e2e8f0;
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
        padding: 1rem;
    }

    .sidebar {
        width: 100%;
        position: static;
    }

    .header-content {
        padding: 0 1rem;
    }

    .exercise-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .exercise-actions {
        justify-content: stretch;
    }

    .action-btn {
        flex: 1;
        justify-content: center;
    }

    .path-cards {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}
