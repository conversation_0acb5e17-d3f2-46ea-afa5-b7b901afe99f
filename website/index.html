<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interview Prep - Coding Practice</title>
    <link rel="stylesheet" href="styles/main.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-code"></i>
                    <h1>Interview Prep</h1>
                </div>
                <div class="progress-summary">
                    <div class="progress-item">
                        <span class="progress-label">Progress</span>
                        <div class="progress-bar">
                            <div class="progress-fill" id="overall-progress"></div>
                        </div>
                        <span class="progress-text" id="progress-text">0/30</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="learning-path">
                    <h2><i class="fas fa-route"></i> Learning Path</h2>
                    
                    <!-- Difficulty Filter -->
                    <div class="difficulty-filter">
                        <button class="filter-btn active" data-difficulty="all">All</button>
                        <button class="filter-btn" data-difficulty="Easy">Easy</button>
                        <button class="filter-btn" data-difficulty="Medium">Medium</button>
                        <button class="filter-btn" data-difficulty="Hard">Hard</button>
                    </div>

                    <!-- Category Filter -->
                    <div class="category-filter">
                        <select id="category-select">
                            <option value="all">All Categories</option>
                            <option value="Arrays">Arrays</option>
                            <option value="Linked List">Linked List</option>
                            <option value="Trees">Trees</option>
                            <option value="Dynamic Programming">Dynamic Programming</option>
                            <option value="Search">Search</option>
                            <option value="Stacks">Stacks</option>
                            <option value="Graphs">Graphs</option>
                            <option value="Design">Design</option>
                            <option value="Bit Manipulation">Bit Manipulation</option>
                            <option value="Intervals">Intervals</option>
                            <option value="Math">Math</option>
                            <option value="Two Pointers">Two Pointers</option>
                        </select>
                    </div>

                    <!-- Exercise List -->
                    <div class="exercise-list" id="exercise-list">
                        <!-- Exercises will be populated by JavaScript -->
                    </div>
                </div>
            </aside>

            <!-- Content Area -->
            <section class="content-area">
                <!-- Welcome Screen -->
                <div class="welcome-screen" id="welcome-screen">
                    <div class="welcome-content">
                        <h2><i class="fas fa-rocket"></i> Welcome to Interview Prep!</h2>
                        <p>Master coding interviews with our structured learning path. Start with easy problems and progress to advanced challenges.</p>
                        
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number" id="total-exercises">30</div>
                                <div class="stat-label">Total Exercises</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="completed-exercises">0</div>
                                <div class="stat-label">Completed</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="easy-count">17</div>
                                <div class="stat-label">Easy</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="medium-count">10</div>
                                <div class="stat-label">Medium</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="hard-count">3</div>
                                <div class="stat-label">Hard</div>
                            </div>
                        </div>

                        <div class="learning-paths">
                            <h3>Choose Your Path</h3>
                            <div class="path-cards">
                                <div class="path-card" data-path="beginner">
                                    <i class="fas fa-seedling"></i>
                                    <h4>Beginner Path</h4>
                                    <p>Start with easy problems to build confidence</p>
                                    <button class="path-btn">Start Easy Problems</button>
                                </div>
                                <div class="path-card" data-path="intermediate">
                                    <i class="fas fa-chart-line"></i>
                                    <h4>Intermediate Path</h4>
                                    <p>Ready for most technical interviews</p>
                                    <button class="path-btn">Start All Problems</button>
                                </div>
                                <div class="path-card" data-path="advanced">
                                    <i class="fas fa-trophy"></i>
                                    <h4>Advanced Path</h4>
                                    <p>Master hard problems for senior roles</p>
                                    <button class="path-btn">Start Hard Problems</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Exercise Detail View -->
                <div class="exercise-detail" id="exercise-detail" style="display: none;">
                    <div class="exercise-header">
                        <div class="exercise-title-section">
                            <h2 id="exercise-title">Exercise Title</h2>
                            <div class="exercise-meta">
                                <span class="difficulty-badge" id="exercise-difficulty">Easy</span>
                                <span class="category-badge" id="exercise-category">Arrays</span>
                                <span class="leetcode-link">
                                    <a href="#" target="_blank" id="leetcode-link">
                                        <i class="fas fa-external-link-alt"></i> LeetCode #<span id="leetcode-number">1</span>
                                    </a>
                                </span>
                            </div>
                        </div>
                        <div class="exercise-actions">
                            <button class="action-btn" id="mark-complete-btn">
                                <i class="fas fa-check"></i> Mark Complete
                            </button>
                            <button class="action-btn secondary" id="view-code-btn">
                                <i class="fas fa-code"></i> View Code
                            </button>
                        </div>
                    </div>

                    <div class="exercise-content">
                        <div class="problem-description">
                            <h3>Problem Description</h3>
                            <p id="exercise-description">Problem description will appear here...</p>
                        </div>

                        <div class="problem-details">
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <h4>Time Complexity</h4>
                                    <span id="time-complexity">O(n)</span>
                                </div>
                                <div class="detail-item">
                                    <h4>Space Complexity</h4>
                                    <span id="space-complexity">O(1)</span>
                                </div>
                                <div class="detail-item">
                                    <h4>Patterns</h4>
                                    <div id="patterns-list">
                                        <!-- Patterns will be populated -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="navigation-buttons">
                            <button class="nav-btn" id="prev-btn" disabled>
                                <i class="fas fa-chevron-left"></i> Previous
                            </button>
                            <button class="nav-btn primary" id="next-btn">
                                Next <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Code Modal -->
    <div class="modal" id="code-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Solution Code</h3>
                <div class="modal-actions">
                    <button class="copy-btn" id="copy-code-btn" title="Copy code to clipboard">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="close-btn" id="close-modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="modal-body">
                <pre><code id="code-content" class="language-java">// Code will be loaded here...</code></pre>
            </div>
        </div>
    </div>

    <script src="scripts/main.js"></script>
</body>
</html>
