{"exercises": [{"id": 1, "title": "Contains Duplicate", "leetcodeNumber": 217, "difficulty": "Easy", "category": "<PERSON><PERSON><PERSON>", "filePath": "src/algorithms/augment/arrays/ContainsDuplicate.java", "description": "Given an integer array nums, return true if any value appears at least twice in the array, and return false if every element is distinct.", "patterns": ["Hash Set", "Sorting"], "timeComplexity": "O(n)", "spaceComplexity": "O(n)"}, {"id": 2, "title": "<PERSON><PERSON>", "leetcodeNumber": 242, "difficulty": "Easy", "category": "<PERSON><PERSON><PERSON>", "filePath": "src/algorithms/augment/arrays/ValidAnagram.java", "description": "Given two strings s and t, return true if t is an anagram of s, and false otherwise.", "patterns": ["Hash Map", "Sorting"], "timeComplexity": "O(n)", "spaceComplexity": "O(1)"}, {"id": 3, "title": "Valid Palindrome", "leetcodeNumber": 125, "difficulty": "Easy", "category": "<PERSON><PERSON><PERSON>", "filePath": "src/algorithms/augment/arrays/ValidPalindrome.java", "description": "A phrase is a palindrome if, after converting all uppercase letters into lowercase letters and removing all non-alphanumeric characters, it reads the same forward and backward.", "patterns": ["Two Pointers"], "timeComplexity": "O(n)", "spaceComplexity": "O(1)"}, {"id": 4, "title": "Best Time to Buy and Sell Stock", "leetcodeNumber": 121, "difficulty": "Easy", "category": "<PERSON><PERSON><PERSON>", "filePath": "src/algorithms/augment/arrays/BestTimeToBuyStock.java", "description": "You are given an array prices where prices[i] is the price of a given stock on the ith day. Return the maximum profit you can achieve from this transaction. If you cannot achieve any profit, return 0.", "patterns": ["Dynamic Programming", "Greedy"], "timeComplexity": "O(n)", "spaceComplexity": "O(1)"}, {"id": 5, "title": "Binary Search", "leetcodeNumber": 704, "difficulty": "Easy", "category": "Search", "filePath": "src/algorithms/augment/search/BinarySearch.java", "description": "Given an array of integers nums which is sorted in ascending order, and an integer target, write a function to search target in nums. You must write an algorithm with O(log n) runtime complexity.", "patterns": ["Binary Search"], "timeComplexity": "O(log n)", "spaceComplexity": "O(1)"}, {"id": 6, "title": "Reverse Linked List", "leetcodeNumber": 206, "difficulty": "Easy", "category": "Linked List", "filePath": "src/algorithms/augment/linkedlist/ReverseLinkedList.java", "description": "Given the head of a singly linked list, reverse the list, and return the reversed list.", "patterns": ["Linked List", "Iterative", "Recursive"], "timeComplexity": "O(n)", "spaceComplexity": "O(1)"}, {"id": 7, "title": "Linked List Cycle", "leetcodeNumber": 141, "difficulty": "Easy", "category": "Linked List", "filePath": "src/algorithms/augment/linkedlist/LinkedListCycle.java", "description": "Given head, the head of a linked list, determine if the linked list has a cycle in it.", "patterns": ["Two Pointers", "Floyd's Cycle Detection"], "timeComplexity": "O(n)", "spaceComplexity": "O(1)"}, {"id": 8, "title": "Merge Two Sorted Lists", "leetcodeNumber": 21, "difficulty": "Easy", "category": "Linked List", "filePath": "src/algorithms/augment/linkedlist/MergeTwoSortedLists.java", "description": "You are given the heads of two sorted linked lists list1 and list2. Merge the two lists in a sorted list.", "patterns": ["Linked List", "Two Pointers"], "timeComplexity": "O(n + m)", "spaceComplexity": "O(1)"}, {"id": 9, "title": "Invert Binary Tree", "leetcodeNumber": 226, "difficulty": "Easy", "category": "Trees", "filePath": "src/algorithms/augment/trees/InvertBinaryTree.java", "description": "Given the root of a binary tree, invert the tree, and return its root.", "patterns": ["Tree", "DFS", "BFS"], "timeComplexity": "O(n)", "spaceComplexity": "O(h)"}, {"id": 10, "title": "Maximum Depth of Binary Tree", "leetcodeNumber": 104, "difficulty": "Easy", "category": "Trees", "filePath": "src/algorithms/augment/trees/MaxDepthBinaryTree.java", "description": "Given the root of a binary tree, return its maximum depth.", "patterns": ["Tree", "DFS", "BFS"], "timeComplexity": "O(n)", "spaceComplexity": "O(h)"}, {"id": 11, "title": "Same Tree", "leetcodeNumber": 100, "difficulty": "Easy", "category": "Trees", "filePath": "src/algorithms/augment/trees/SameTree.java", "description": "Given the roots of two binary trees p and q, write a function to check if they are the same or not.", "patterns": ["Tree", "DFS"], "timeComplexity": "O(n)", "spaceComplexity": "O(h)"}, {"id": 12, "title": "Valid Parentheses", "leetcodeNumber": 20, "difficulty": "Easy", "category": "Stacks", "filePath": "src/algorithms/augment/stacks/ValidParentheses.java", "description": "Given a string s containing just the characters '(', ')', '{', '}', '[' and ']', determine if the input string is valid.", "patterns": ["<PERSON><PERSON>"], "timeComplexity": "O(n)", "spaceComplexity": "O(n)"}, {"id": 13, "title": "Climbing Stairs", "leetcodeNumber": 70, "difficulty": "Easy", "category": "Dynamic Programming", "filePath": "src/algorithms/augment/dp/ClimbingStairs.java", "description": "You are climbing a staircase. It takes n steps to reach the top. Each time you can either climb 1 or 2 steps. In how many distinct ways can you climb to the top?", "patterns": ["Dynamic Programming", "<PERSON><PERSON><PERSON><PERSON>"], "timeComplexity": "O(n)", "spaceComplexity": "O(1)"}, {"id": 14, "title": "Single Number", "leetcodeNumber": 136, "difficulty": "Easy", "category": "Bit Manipulation", "filePath": "src/algorithms/augment/bitmanipulation/SingleNumber.java", "description": "Given a non-empty array of integers nums, every element appears twice except for one. Find that single one.", "patterns": ["Bit Manipulation", "XOR"], "timeComplexity": "O(n)", "spaceComplexity": "O(1)"}, {"id": 15, "title": "Number of 1 Bits", "leetcodeNumber": 191, "difficulty": "Easy", "category": "Bit Manipulation", "filePath": "src/algorithms/augment/bitmanipulation/NumberOf1Bits.java", "description": "Write a function that takes an unsigned integer and returns the number of '1' bits it has (also known as the Hamming weight).", "patterns": ["Bit Manipulation"], "timeComplexity": "O(1)", "spaceComplexity": "O(1)"}, {"id": 16, "title": "Meeting Rooms", "leetcodeNumber": 252, "difficulty": "Easy", "category": "Intervals", "filePath": "src/algorithms/augment/intervals/MeetingRooms.java", "description": "Given an array of meeting time intervals where intervals[i] = [starti, endi], determine if a person could attend all meetings.", "patterns": ["Intervals", "Sorting"], "timeComplexity": "O(n log n)", "spaceComplexity": "O(1)"}, {"id": 17, "title": "Happy Number", "leetcodeNumber": 202, "difficulty": "Easy", "category": "Math", "filePath": "src/algorithms/augment/math/HappyNumber.java", "description": "Write an algorithm to determine if a number n is happy.", "patterns": ["Hash Set", "Two Pointers"], "timeComplexity": "O(log n)", "spaceComplexity": "O(log n)"}, {"id": 18, "title": "Group Anagrams", "leetcodeNumber": 49, "difficulty": "Medium", "category": "<PERSON><PERSON><PERSON>", "filePath": "src/algorithms/augment/arrays/GroupAnagrams.java", "description": "Given an array of strings strs, group the anagrams together.", "patterns": ["Hash Map", "Sorting"], "timeComplexity": "O(n * k log k)", "spaceComplexity": "O(n * k)"}, {"id": 19, "title": "Top K Frequent Elements", "leetcodeNumber": 347, "difficulty": "Medium", "category": "<PERSON><PERSON><PERSON>", "filePath": "src/algorithms/augment/arrays/TopKFrequentElements.java", "description": "Given an integer array nums and an integer k, return the k most frequent elements.", "patterns": ["Hash Map", "<PERSON><PERSON>", "Bucket Sort"], "timeComplexity": "O(n log k)", "spaceComplexity": "O(n)"}, {"id": 20, "title": "Product of Array Except Self", "leetcodeNumber": 238, "difficulty": "Medium", "category": "<PERSON><PERSON><PERSON>", "filePath": "src/algorithms/augment/arrays/ProductExceptSelf.java", "description": "Given an integer array nums, return an array answer such that answer[i] is equal to the product of all the elements of nums except nums[i]. You must write an algorithm that runs in O(n) time and without using the division operation.", "patterns": ["Array", "Prefix Sum"], "timeComplexity": "O(n)", "spaceComplexity": "O(1)"}, {"id": 21, "title": "3Sum", "leetcodeNumber": 15, "difficulty": "Medium", "category": "<PERSON><PERSON><PERSON>", "filePath": "src/algorithms/augment/arrays/ThreeSum.java", "description": "Given an integer array nums, return all the triplets [nums[i], nums[j], nums[k]] such that i != j, i != k, and j != k, and nums[i] + nums[j] + nums[k] == 0. Notice that the solution set must not contain duplicate triplets.", "patterns": ["Two Pointers", "Sorting"], "timeComplexity": "O(n²)", "spaceComplexity": "O(1)"}, {"id": 22, "title": "Container With Most Water", "leetcodeNumber": 11, "difficulty": "Medium", "category": "<PERSON><PERSON><PERSON>", "filePath": "src/algorithms/augment/arrays/ContainerWithMostWater.java", "description": "You are given an integer array height of length n. There are n vertical lines drawn such that the two endpoints of the ith line are (i, 0) and (i, height[i]). Find two lines that together with the x-axis form a container that contains the most water.", "patterns": ["Two Pointers"], "timeComplexity": "O(n)", "spaceComplexity": "O(1)"}, {"id": 23, "title": "Longest Substring Without Repeating Characters", "leetcodeNumber": 3, "difficulty": "Medium", "category": "<PERSON><PERSON><PERSON>", "filePath": "src/algorithms/augment/arrays/LongestSubstringWithoutRepeating.java", "description": "Given a string s, find the length of the longest substring without repeating characters.", "patterns": ["Sliding Window", "Hash Set"], "timeComplexity": "O(n)", "spaceComplexity": "O(min(m,n))"}, {"id": 24, "title": "Maximum Subarray", "leetcodeNumber": 53, "difficulty": "Medium", "category": "<PERSON><PERSON><PERSON>", "filePath": "src/algorithms/augment/arrays/MaxSubarray.java", "description": "Given an integer array nums, find the contiguous subarray (containing at least one number) which has the largest sum and return its sum.", "patterns": ["Dynamic Programming", "<PERSON><PERSON><PERSON>'s <PERSON><PERSON><PERSON><PERSON>"], "timeComplexity": "O(n)", "spaceComplexity": "O(1)"}, {"id": 25, "title": "Coin Change", "leetcodeNumber": 322, "difficulty": "Medium", "category": "Dynamic Programming", "filePath": "src/algorithms/augment/dp/CoinChange.java", "description": "You are given an integer array coins representing coins of different denominations and an integer amount representing a total amount of money. Return the fewest number of coins that you need to make up that amount. If that amount of money cannot be made up by any combination of the coins, return -1.", "patterns": ["Dynamic Programming"], "timeComplexity": "O(amount * coins)", "spaceComplexity": "O(amount)"}, {"id": 26, "title": "Number of Islands", "leetcodeNumber": 200, "difficulty": "Medium", "category": "Graphs", "filePath": "src/algorithms/augment/graphs/NumberOfIslands.java", "description": "Given an m x n 2D binary grid which represents a map of '1's (land) and '0's (water), return the number of islands. An island is surrounded by water and is formed by connecting adjacent lands horizontally or vertically.", "patterns": ["DFS", "BFS", "Union Find"], "timeComplexity": "O(m * n)", "spaceComplexity": "O(m * n)"}, {"id": 27, "title": "LRU Cache", "leetcodeNumber": 146, "difficulty": "Medium", "category": "Design", "filePath": "src/algorithms/augment/design/LRUCache.java", "description": "Design a data structure that follows the constraints of a Least Recently Used (LRU) cache. The functions get and put must each run in O(1) average time complexity.", "patterns": ["Hash Map", "Doubly Linked List"], "timeComplexity": "O(1)", "spaceComplexity": "O(capacity)"}, {"id": 28, "title": "Trapping Rain Water", "leetcodeNumber": 42, "difficulty": "Hard", "category": "Two Pointers", "filePath": "src/algorithms/augment/twopointers/TrappingRainWater.java", "description": "Given n non-negative integers representing an elevation map where the width of each bar is 1, compute how much water it can trap after raining.", "patterns": ["Two Pointers", "Dynamic Programming", "<PERSON><PERSON>"], "timeComplexity": "O(n)", "spaceComplexity": "O(1)"}, {"id": 29, "title": "Sliding Window Maximum", "leetcodeNumber": 239, "difficulty": "Hard", "category": "<PERSON><PERSON><PERSON>", "filePath": "src/algorithms/augment/arrays/SlidingWindowMaximum.java", "description": "You are given an array of integers nums, there is a sliding window of size k which is moving from the very left of the array to the very right. You can only see the k numbers in the window. Each time the sliding window moves right by one position. Return the max sliding window.", "patterns": ["Sliding Window", "<PERSON><PERSON>", "Monotonic Queue"], "timeComplexity": "O(n)", "spaceComplexity": "O(k)"}, {"id": 30, "title": "Serialize and Deserialize Binary Tree", "leetcodeNumber": 297, "difficulty": "Hard", "category": "Trees", "filePath": "src/algorithms/augment/trees/SerializeDeserializeBinaryTree.java", "description": "Serialization is the process of converting a data structure or object into a sequence of bits so that it can be stored in a file or memory buffer, or transmitted across a network connection link to be reconstructed later in the same or another computer environment.", "patterns": ["Tree", "DFS", "BFS", "String"], "timeComplexity": "O(n)", "spaceComplexity": "O(n)"}]}