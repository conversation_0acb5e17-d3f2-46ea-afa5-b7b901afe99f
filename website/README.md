# Interview Prep Website

A beautiful, interactive web application for following a structured coding interview preparation program.

## Features

### 🎯 **Structured Learning Path**
- **Beginner Path**: Start with Easy problems to build confidence
- **Intermediate Path**: Progress through all difficulty levels
- **Advanced Path**: Focus on Hard problems for senior roles

### 📊 **Progress Tracking**
- Visual progress bar showing completion percentage
- Local storage to persist your progress across sessions
- Statistics dashboard with completion counts by difficulty

### 🔍 **Smart Filtering**
- Filter exercises by difficulty (Easy, Medium, Hard)
- Filter by category (Arrays, Trees, DP, etc.)
- Search and navigate through 30+ carefully selected problems

### 💡 **Exercise Details**
- Complete problem descriptions from LeetCode
- Time and space complexity information
- Algorithm patterns and techniques used
- Direct links to LeetCode problems

### 🎨 **Modern UI/UX**
- Beautiful gradient design with glassmorphism effects
- Responsive layout that works on all devices
- Smooth animations and transitions
- Keyboard shortcuts for navigation

## Getting Started

### Recommended: Node.js Server (Best Experience)
1. Open the `website` folder in your terminal
2. Start the Node.js server:
   ```bash
   cd website
   node server.js
   ```
3. Open your browser and go to `http://localhost:8000`

**Why Node.js server?** It can serve both the website files AND the Java source code files from your project, enabling the "View Code" feature to work properly.

### Alternative: Python Server (Limited Code Viewing)
1. Open the `website` folder in your terminal
2. Start a simple HTTP server:
   ```bash
   # Python 3
   python -m http.server 8000
   ```
3. Open your browser and go to `http://localhost:8000`

**Note:** With Python server, the "View Code" feature will show placeholder content instead of actual Java files due to CORS restrictions.

### VS Code Live Server (Limited Code Viewing)
1. Install the "Live Server" extension in VS Code
2. Right-click on `index.html` and select "Open with Live Server"

**Note:** Same limitation as Python server - code viewing will be limited.

## File Structure

```
website/
├── index.html              # Main HTML file
├── styles/
│   └── main.css           # All styling and responsive design
├── scripts/
│   └── main.js            # Application logic and interactivity
├── data/
│   └── exercises.json     # Exercise data with 30+ problems
└── README.md              # This file
```

## Exercise Data

The website includes 30+ carefully selected coding problems:

- **17 Easy Problems**: Build fundamentals and confidence
- **10 Medium Problems**: Core interview-level challenges  
- **3 Hard Problems**: Advanced problems for senior roles

Each exercise includes:
- Problem title and LeetCode number
- Difficulty level and category
- Complete problem description
- Time/space complexity
- Algorithm patterns used
- File path to the Java solution

## Keyboard Shortcuts

- **←/→ Arrow Keys**: Navigate between exercises
- **Escape**: Close modal dialogs

## Local Storage

Your progress is automatically saved to browser local storage, so you can:
- Close and reopen the website without losing progress
- Track which problems you've completed
- Resume where you left off

## Customization

### Adding New Exercises
Edit `data/exercises.json` to add new problems:

```json
{
  "id": 31,
  "title": "New Problem",
  "leetcodeNumber": 123,
  "difficulty": "Medium",
  "category": "Arrays",
  "filePath": "src/path/to/solution.java",
  "description": "Problem description...",
  "patterns": ["Pattern1", "Pattern2"],
  "timeComplexity": "O(n)",
  "spaceComplexity": "O(1)"
}
```

### Styling Changes
Modify `styles/main.css` to customize:
- Color scheme (update CSS custom properties)
- Layout and spacing
- Animations and transitions

### Functionality Changes
Edit `scripts/main.js` to add:
- New filtering options
- Additional statistics
- Custom learning paths

## Browser Compatibility

- Chrome/Edge: Full support
- Firefox: Full support  
- Safari: Full support
- Mobile browsers: Responsive design works on all devices

## Future Enhancements

Potential features to add:
- Code syntax highlighting in modal
- Timer for practice sessions
- Difficulty-based scoring system
- Export progress reports
- Integration with actual code files
- Spaced repetition algorithm
- Study notes and bookmarks

## Contributing

To contribute to this project:
1. Fork the repository
2. Make your changes
3. Test thoroughly across different browsers
4. Submit a pull request

## License

This project is open source and available under the MIT License.
