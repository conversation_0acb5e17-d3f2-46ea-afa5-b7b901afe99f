# Social Media News Feed System Design

Design a social media news feed system like Twitter Timeline, Facebook News Feed, or Instagram Feed that shows personalized content to users.

## 📋 Requirements

### Functional Requirements
1. **Post Creation**: Users can create posts (text, images, videos)
2. **News Feed**: Generate personalized feed for each user
3. **Follow System**: Users can follow/unfollow other users
4. **Interactions**: Like, comment, share posts
5. **Timeline Types**: Home feed (following) and user profile feed
6. **Real-time Updates**: New posts appear in feeds in real-time

### Non-Functional Requirements
1. **Scale**: 300M daily active users, 200M posts per day
2. **Feed Generation**: < 200ms for feed loading
3. **Availability**: 99.9% uptime
4. **Consistency**: Eventually consistent (acceptable for social media)
5. **Storage**: Posts stored indefinitely

## 🔢 Capacity Estimation

### Traffic Estimates
- **Daily posts**: 200M posts/day = ~2,300 posts/second
- **Feed requests**: 300M users × 5 feeds/day = 1.5B requests/day = ~17,400 requests/second
- **Peak traffic**: 3x average = ~52,200 feed requests/second
- **Read:Write ratio**: 100:1 (heavy read workload)

### Storage Estimates
- **Posts per year**: 200M × 365 = 73B posts
- **Storage per post**: ~1KB (text + metadata)
- **Media storage**: ~500KB average per post
- **Total storage**: 73B × 1KB + 73B × 500KB = ~36.5PB per year

### Memory Estimates
- **Active users**: 300M users
- **Feed cache per user**: ~1MB (recent posts)
- **Total cache**: 300M × 1MB = ~300TB

## 🏗️ High-Level Architecture

```mermaid
graph TB
    subgraph "Client Applications"
        MobileApp[Mobile Apps<br/>iOS/Android]
        WebApp[Web Application<br/>React/Angular]
        TabletApp[Tablet Apps<br/>Responsive UI]
    end

    subgraph "Content Delivery & Load Balancing"
        CDN[Global CDN<br/>Static Content]
        GlobalLB[Global Load Balancer<br/>Geographic Routing]
        RegionalLB[Regional Load Balancers<br/>Traffic Distribution]
    end

    subgraph "API Gateway Layer"
        APIGateway[API Gateway<br/>Authentication & Rate Limiting]
        GraphQLGateway[GraphQL Gateway<br/>Unified Data Layer]
    end

    subgraph "Core Application Services"
        PostService[Post Service<br/>CRUD Operations]
        FeedService[Feed Service<br/>Timeline Generation]
        UserService[User Service<br/>Profile Management]
        RelationshipService[Relationship Service<br/>Follow/Friend Logic]
        NotificationService[Notification Service<br/>Real-time Updates]
        MediaService[Media Service<br/>Image/Video Processing]
    end

    subgraph "Feed Generation Engine"
        FeedGenerator[Feed Generator<br/>Timeline Assembly]
        RankingService[Ranking Service<br/>ML-based Scoring]
        ContentFilter[Content Filter<br/>Personalization]
        FeedCache[Feed Cache<br/>Redis Cluster]
        TrendingService[Trending Service<br/>Popular Content]
    end

    subgraph "Real-time Systems"
        WebSocketGateway[WebSocket Gateway<br/>Live Updates]
        EventStream[Event Stream<br/>Apache Kafka]
        PushNotificationService[Push Service<br/>Mobile Notifications]
    end

    subgraph "Message Queue System"
        PostCreationQueue[Post Creation Queue<br/>Async Processing]
        FeedUpdateQueue[Feed Update Queue<br/>Timeline Refresh]
        NotificationQueue[Notification Queue<br/>User Alerts]
        AnalyticsQueue[Analytics Queue<br/>Engagement Tracking]
    end

    subgraph "Storage Systems"
        PostDB[(Post Database<br/>MongoDB/Cassandra)]
        UserDB[(User Database<br/>PostgreSQL)]
        GraphDB[(Social Graph<br/>Neo4j/Amazon Neptune)]
        MediaStorage[(Media Storage<br/>S3/CloudFront)]
        SearchIndex[(Search Index<br/>Elasticsearch)]
        TimelineCache[Timeline Cache<br/>Redis Cluster]
        SessionStore[Session Store<br/>Redis]
    end

    subgraph "Machine Learning & Analytics"
        MLPipeline[ML Pipeline<br/>Recommendation Training]
        FeatureStore[Feature Store<br/>User/Content Features]
        AnalyticsEngine[Analytics Engine<br/>Apache Spark]
        DataWarehouse[(Data Warehouse<br/>BigQuery/Snowflake)]
        ABTestingService[A/B Testing<br/>Experiment Framework]
    end

    subgraph "External Services"
        ImageProcessing[Image Processing<br/>Thumbnail Generation]
        VideoTranscoding[Video Transcoding<br/>Multiple Formats]
        ContentModeration[Content Moderation<br/>AI/Human Review]
        EmailService[Email Service<br/>Notifications]
        SMSService[SMS Service<br/>Verification]
    end

    %% Client connections
    MobileApp --> CDN
    WebApp --> CDN
    TabletApp --> CDN

    %% CDN and Load Balancing
    CDN --> GlobalLB
    GlobalLB --> RegionalLB
    RegionalLB --> APIGateway
    RegionalLB --> GraphQLGateway

    %% API Gateway to Services
    APIGateway --> PostService
    APIGateway --> FeedService
    APIGateway --> UserService
    APIGateway --> RelationshipService
    APIGateway --> MediaService

    GraphQLGateway --> PostService
    GraphQLGateway --> FeedService
    GraphQLGateway --> UserService

    %% Real-time connections
    WebSocketGateway --> MobileApp
    WebSocketGateway --> WebApp
    WebSocketGateway --> TabletApp

    %% Feed Generation Flow
    FeedService --> FeedGenerator
    FeedGenerator --> RankingService
    RankingService --> ContentFilter
    ContentFilter --> FeedCache
    FeedGenerator --> TrendingService

    %% Post Creation Flow
    PostService --> PostCreationQueue
    PostCreationQueue --> FeedUpdateQueue
    PostCreationQueue --> NotificationQueue
    PostCreationQueue --> AnalyticsQueue

    %% Queue Processing
    FeedUpdateQueue --> FeedGenerator
    NotificationQueue --> NotificationService
    AnalyticsQueue --> AnalyticsEngine

    %% Storage Connections
    PostService --> PostDB
    UserService --> UserDB
    RelationshipService --> GraphDB
    MediaService --> MediaStorage
    FeedService --> SearchIndex

    %% Caching
    FeedService --> TimelineCache
    UserService --> SessionStore
    PostService --> FeedCache

    %% Real-time Events
    PostService --> EventStream
    EventStream --> WebSocketGateway
    EventStream --> PushNotificationService

    %% ML and Analytics
    AnalyticsEngine --> DataWarehouse
    DataWarehouse --> MLPipeline
    MLPipeline --> FeatureStore
    FeatureStore --> RankingService
    ABTestingService --> RankingService

    %% External Services
    MediaService --> ImageProcessing
    MediaService --> VideoTranscoding
    PostService --> ContentModeration
    NotificationService --> EmailService
    UserService --> SMSService

    classDef client fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef cdn fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef gateway fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef service fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef feed fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef realtime fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef queue fill:#e0f2f1,stroke:#00796b,stroke-width:2px
    classDef storage fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
    classDef ml fill:#fff8e1,stroke:#ffa000,stroke-width:2px
    classDef external fill:#ffebee,stroke:#d32f2f,stroke-width:2px

    class MobileApp,WebApp,TabletApp client
    class CDN,GlobalLB,RegionalLB cdn
    class APIGateway,GraphQLGateway gateway
    class PostService,FeedService,UserService,RelationshipService,NotificationService,MediaService service
    class FeedGenerator,RankingService,ContentFilter,FeedCache,TrendingService feed
    class WebSocketGateway,EventStream,PushNotificationService realtime
    class PostCreationQueue,FeedUpdateQueue,NotificationQueue,AnalyticsQueue queue
    class PostDB,UserDB,GraphDB,MediaStorage,SearchIndex,TimelineCache,SessionStore storage
    class MLPipeline,FeatureStore,AnalyticsEngine,DataWarehouse,ABTestingService ml
    class ImageProcessing,VideoTranscoding,ContentModeration,EmailService,SMSService external
```

## 🔧 Detailed Design

### 1. Database Schema

**Posts Table:**
```sql
CREATE TABLE posts (
    post_id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    content TEXT,
    media_urls JSON,
    post_type ENUM('text', 'image', 'video', 'link'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    like_count INT DEFAULT 0,
    comment_count INT DEFAULT 0,
    share_count INT DEFAULT 0,
    INDEX idx_user_id_created (user_id, created_at),
    INDEX idx_created_at (created_at)
);
```

**User Relationships (Social Graph):**
```sql
CREATE TABLE user_relationships (
    follower_id VARCHAR(36),
    following_id VARCHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    relationship_type ENUM('follow', 'friend', 'block'),
    PRIMARY KEY (follower_id, following_id),
    INDEX idx_following_id (following_id),
    INDEX idx_follower_id (follower_id)
);
```

**Post Interactions:**
```sql
CREATE TABLE post_likes (
    post_id VARCHAR(36),
    user_id VARCHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (post_id, user_id),
    INDEX idx_user_id (user_id)
);

CREATE TABLE post_comments (
    comment_id VARCHAR(36) PRIMARY KEY,
    post_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    content TEXT NOT NULL,
    parent_comment_id VARCHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_post_id_created (post_id, created_at)
);
```

### 2. Feed Generation Strategies

**Pull Model (On-demand):**
```java
public List<Post> generateFeed(String userId, int limit) {
    // Get user's following list
    List<String> followingIds = getFollowingList(userId);
    
    // Fetch recent posts from followed users
    List<Post> posts = postService.getRecentPosts(followingIds, limit * 2);
    
    // Rank and filter posts
    List<Post> rankedPosts = rankingService.rankPosts(posts, userId);
    
    return rankedPosts.subList(0, Math.min(limit, rankedPosts.size()));
}
```

**Push Model (Pre-computed):**
```java
@EventListener
public void onPostCreated(PostCreatedEvent event) {
    Post post = event.getPost();
    
    // Get followers of the post author
    List<String> followerIds = userService.getFollowers(post.getUserId());
    
    // Add post to each follower's feed cache
    for (String followerId : followerIds) {
        feedCache.addToFeed(followerId, post);
    }
}
```

**Hybrid Model:**
```java
public List<Post> generateHybridFeed(String userId, int limit) {
    // For users with few followers: use push model (pre-computed)
    if (getFollowerCount(userId) < 1000) {
        return getCachedFeed(userId, limit);
    }
    
    // For celebrities: use pull model (on-demand)
    return generateFeedOnDemand(userId, limit);
}
```

### 3. Ranking Algorithm

**Basic Ranking Factors:**
```java
public double calculatePostScore(Post post, String userId) {
    double score = 0.0;
    
    // Recency factor (newer posts get higher score)
    long ageInHours = (System.currentTimeMillis() - post.getCreatedAt()) / (1000 * 60 * 60);
    double recencyScore = Math.exp(-ageInHours / 24.0); // Decay over 24 hours
    
    // Engagement factor
    double engagementScore = Math.log(1 + post.getLikeCount() + post.getCommentCount() * 2);
    
    // User affinity (how much user interacts with post author)
    double affinityScore = getUserAffinity(userId, post.getUserId());
    
    // Content type preference
    double contentTypeScore = getContentTypePreference(userId, post.getPostType());
    
    score = recencyScore * 0.3 + engagementScore * 0.3 + affinityScore * 0.3 + contentTypeScore * 0.1;
    
    return score;
}
```

### 4. Caching Strategy

**Feed Cache Structure (Redis):**
```redis
# User's feed cache (sorted set by score/timestamp)
ZADD user:feed:user_123 1642234567 post_456
ZADD user:feed:user_123 1642234568 post_789

# Post cache (hash)
HSET post:456 "user_id" "user_789" "content" "Hello world!" "created_at" "1642234567"

# User's timeline cache
ZADD user:timeline:user_123 1642234567 post_456
```

**Cache Management:**
```java
public class FeedCacheManager {
    private static final int FEED_CACHE_SIZE = 1000;
    private static final int CACHE_TTL_HOURS = 24;
    
    public void updateFeedCache(String userId, List<Post> posts) {
        String cacheKey = "user:feed:" + userId;
        
        // Remove old entries to maintain cache size
        redisTemplate.opsForZSet().removeRange(cacheKey, 0, -FEED_CACHE_SIZE);
        
        // Add new posts with timestamp as score
        for (Post post : posts) {
            redisTemplate.opsForZSet().add(cacheKey, post.getPostId(), post.getCreatedAt());
        }
        
        // Set expiration
        redisTemplate.expire(cacheKey, CACHE_TTL_HOURS, TimeUnit.HOURS);
    }
}
```

### 5. Real-time Updates

**WebSocket for Real-time Feed Updates:**
```javascript
// Client-side WebSocket connection
const feedSocket = new WebSocket('wss://api.social.com/feed-updates');

feedSocket.onmessage = function(event) {
    const update = JSON.parse(event.data);
    if (update.type === 'new_post') {
        prependPostToFeed(update.post);
    } else if (update.type === 'post_update') {
        updatePostInFeed(update.post);
    }
};
```

**Server-side Real-time Updates:**
```java
@EventListener
public void onPostCreated(PostCreatedEvent event) {
    Post post = event.getPost();
    
    // Get online followers
    List<String> onlineFollowers = getOnlineFollowers(post.getUserId());
    
    // Send real-time update
    for (String followerId : onlineFollowers) {
        webSocketService.sendToUser(followerId, new FeedUpdate("new_post", post));
    }
}
```

## ⚡ Scalability Solutions

### 1. Database Sharding

**Posts Sharding:**
```
Shard by user_id:
- Shard 1: user_id hash % 4 == 0
- Shard 2: user_id hash % 4 == 1
- Shard 3: user_id hash % 4 == 2
- Shard 4: user_id hash % 4 == 3
```

**Social Graph Sharding:**
```
Shard by follower_id for read optimization:
- Most queries are "get following list for user X"
- Co-locate follower relationships on same shard
```

### 2. Feed Generation Optimization

**Celebrity User Handling:**
```java
public class CelebrityFeedHandler {
    private static final int CELEBRITY_THRESHOLD = 1_000_000; // 1M followers
    
    public void handleCelebrityPost(Post post) {
        if (getFollowerCount(post.getUserId()) > CELEBRITY_THRESHOLD) {
            // Don't push to all followers immediately
            // Use pull model with aggressive caching
            cachePopularPost(post);
            scheduleAsyncFeedUpdate(post);
        }
    }
}
```

### 3. Content Delivery Network (CDN)

**Media Optimization:**
```java
public class MediaService {
    public String uploadMedia(MultipartFile file, String userId) {
        // Upload original file
        String originalUrl = s3Service.upload(file, "originals/" + userId);
        
        // Generate multiple resolutions
        generateThumbnails(originalUrl);
        
        // Return CDN URL
        return cdnService.getCdnUrl(originalUrl);
    }
    
    private void generateThumbnails(String originalUrl) {
        // Generate different sizes: 150x150, 300x300, 600x600, 1200x1200
        imageProcessingQueue.add(new ThumbnailGenerationTask(originalUrl));
    }
}
```

## 📊 Monitoring & Analytics

### Key Metrics
- **Feed generation latency**: Time to generate user feed
- **Cache hit ratio**: Percentage of feeds served from cache
- **Post engagement rate**: Likes/comments per post
- **User session duration**: Time spent browsing feed
- **Feed refresh rate**: How often users refresh their feed

### A/B Testing Framework
```java
public class FeedExperimentService {
    public List<Post> getFeed(String userId, int limit) {
        String experimentGroup = getExperimentGroup(userId);
        
        switch (experimentGroup) {
            case "chronological":
                return getChronologicalFeed(userId, limit);
            case "engagement_based":
                return getEngagementBasedFeed(userId, limit);
            case "ml_ranked":
                return getMlRankedFeed(userId, limit);
            default:
                return getDefaultFeed(userId, limit);
        }
    }
}
```

## 🔄 Advanced Features

### Machine Learning Integration
- **Collaborative Filtering**: Recommend posts based on similar users
- **Content-based Filtering**: Recommend based on user's past interactions
- **Deep Learning Models**: Neural networks for complex ranking

### Content Moderation
- **Automated Detection**: AI-based inappropriate content detection
- **User Reporting**: Community-based moderation system
- **Shadow Banning**: Reduce visibility of problematic content

This social media feed design provides a scalable, personalized experience that can handle hundreds of millions of users while maintaining fast feed generation and real-time updates.
