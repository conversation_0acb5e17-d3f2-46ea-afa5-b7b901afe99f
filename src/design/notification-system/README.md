# Push Notification System Design

Design a scalable push notification system that can send millions of notifications across multiple channels (push, email, SMS) with high delivery rates and low latency.

## 📋 Requirements

### Functional Requirements
1. **Multi-channel Support**: Push notifications, email, SMS, in-app notifications
2. **Template Management**: Create and manage notification templates
3. **User Preferences**: Users can configure notification preferences
4. **Scheduling**: Send notifications immediately or schedule for later
5. **Targeting**: Send to specific users, groups, or segments
6. **Delivery Tracking**: Track delivery status and engagement metrics
7. **Rate Limiting**: Respect platform limits and user preferences

### Non-Functional Requirements
1. **Scale**: Send 100M notifications per day
2. **Latency**: < 1 second for real-time notifications
3. **Availability**: 99.9% uptime
4. **Delivery Rate**: > 95% successful delivery
5. **Throughput**: Handle 10K notifications per second

## 🔢 Capacity Estimation

### Traffic Estimates
- **Daily notifications**: 100M notifications/day
- **Peak throughput**: ~5K notifications/second (assuming 5x average)
- **Channel distribution**: 60% push, 30% email, 10% SMS
- **Template usage**: ~1000 active templates

### Storage Estimates
- **Notification record**: ~500 bytes per notification
- **Daily storage**: 100M × 500 bytes = 50GB/day
- **Template storage**: 1000 × 10KB = 10MB
- **User preferences**: 10M users × 1KB = 10GB

### Processing Estimates
- **Template rendering**: ~10ms per notification
- **External API calls**: ~100ms per notification
- **Database operations**: ~5ms per notification

## 🏗️ High-Level Architecture

```mermaid
graph TB
    subgraph "Client Applications"
        WebApp[Web Applications<br/>Admin Dashboard]
        MobileApp[Mobile Apps<br/>End Users]
        BackendServices[Backend Services<br/>Microservices]
        ThirdPartyAPI[Third-party APIs<br/>External Integrations]
    end

    subgraph "API Gateway & Load Balancing"
        APIGateway[API Gateway<br/>Authentication & Routing]
        LoadBalancer[Load Balancer<br/>Traffic Distribution]
        RateLimiter[Rate Limiter<br/>API Throttling]
    end

    subgraph "Core API Services"
        NotificationAPI[Notification API<br/>Send/Schedule/Manage]
        TemplateAPI[Template API<br/>Template CRUD]
        PreferencesAPI[Preferences API<br/>User Settings]
        AnalyticsAPI[Analytics API<br/>Delivery Reports]
        WebhookAPI[Webhook API<br/>Delivery Callbacks]
    end

    subgraph "Processing & Orchestration"
        NotificationProcessor[Notification Processor<br/>Message Orchestration]
        TemplateEngine[Template Engine<br/>Handlebars/Mustache]
        SegmentationService[Segmentation Service<br/>User Targeting]
        SchedulingService[Scheduling Service<br/>Time-based Delivery]
        PersonalizationService[Personalization Service<br/>Content Customization]
    end

    subgraph "Message Queue System"
        PriorityQueue[Priority Queue<br/>High/Medium/Low]
        DelayedQueue[Delayed Queue<br/>Scheduled Messages]
        RetryQueue[Retry Queue<br/>Failed Deliveries]
        DeadLetterQueue[Dead Letter Queue<br/>Permanent Failures]
        BulkQueue[Bulk Queue<br/>Mass Notifications]
    end

    subgraph "Channel Handlers"
        PushHandler[Push Handler<br/>Mobile Notifications]
        EmailHandler[Email Handler<br/>SMTP/API Delivery]
        SMSHandler[SMS Handler<br/>Text Messages]
        InAppHandler[In-App Handler<br/>Web/Mobile Banners]
        WebhookHandler[Webhook Handler<br/>HTTP Callbacks]
        SlackHandler[Slack Handler<br/>Team Notifications]
    end

    subgraph "External Service Providers"
        FCM[Firebase Cloud Messaging<br/>Android Push]
        APNS[Apple Push Notification<br/>iOS Push]
        EmailProviders[Email Providers<br/>SendGrid/Mailgun/SES]
        SMSProviders[SMS Providers<br/>Twilio/Nexmo/AWS SNS]
        SlackAPI[Slack API<br/>Workspace Integration]
        WebPushService[Web Push Service<br/>Browser Notifications]
    end

    subgraph "Storage Systems"
        NotificationDB[(Notification Database<br/>MongoDB/PostgreSQL)]
        TemplateDB[(Template Database<br/>PostgreSQL)]
        UserPrefsDB[(User Preferences<br/>Redis/PostgreSQL)]
        AnalyticsDB[(Analytics Database<br/>ClickHouse/BigQuery)]
        DeliveryLogDB[(Delivery Log<br/>Time Series DB)]
        Cache[Distributed Cache<br/>Redis Cluster]
    end

    subgraph "Analytics & Monitoring"
        DeliveryTracker[Delivery Tracker<br/>Status Updates]
        AnalyticsService[Analytics Service<br/>Metrics Aggregation]
        MetricsCollector[Metrics Collector<br/>Prometheus]
        ReportingService[Reporting Service<br/>Dashboard Data]
        ABTestingService[A/B Testing<br/>Campaign Optimization]
    end

    subgraph "Infrastructure Services"
        ConfigService[Config Service<br/>Feature Flags]
        HealthChecker[Health Checker<br/>Service Monitoring]
        LogAggregator[Log Aggregator<br/>ELK Stack]
        AlertManager[Alert Manager<br/>System Alerts]
        BackupService[Backup Service<br/>Data Protection]
    end

    %% Client connections
    WebApp --> APIGateway
    MobileApp --> APIGateway
    BackendServices --> APIGateway
    ThirdPartyAPI --> APIGateway

    %% API Gateway flow
    APIGateway --> LoadBalancer
    LoadBalancer --> RateLimiter
    RateLimiter --> NotificationAPI
    RateLimiter --> TemplateAPI
    RateLimiter --> PreferencesAPI
    RateLimiter --> AnalyticsAPI
    RateLimiter --> WebhookAPI

    %% Processing flow
    NotificationAPI --> NotificationProcessor
    NotificationProcessor --> TemplateEngine
    NotificationProcessor --> SegmentationService
    NotificationProcessor --> SchedulingService
    NotificationProcessor --> PersonalizationService

    %% Queue routing
    NotificationProcessor --> PriorityQueue
    SchedulingService --> DelayedQueue
    NotificationProcessor --> BulkQueue

    %% Queue to handlers
    PriorityQueue --> PushHandler
    PriorityQueue --> EmailHandler
    PriorityQueue --> SMSHandler
    PriorityQueue --> InAppHandler
    PriorityQueue --> WebhookHandler
    PriorityQueue --> SlackHandler

    DelayedQueue --> PriorityQueue
    RetryQueue --> PriorityQueue

    %% Handler to external services
    PushHandler --> FCM
    PushHandler --> APNS
    PushHandler --> WebPushService
    EmailHandler --> EmailProviders
    SMSHandler --> SMSProviders
    SlackHandler --> SlackAPI

    %% Storage connections
    NotificationProcessor --> NotificationDB
    TemplateEngine --> TemplateDB
    SegmentationService --> UserPrefsDB
    AnalyticsService --> AnalyticsDB
    DeliveryTracker --> DeliveryLogDB
    NotificationAPI --> Cache

    %% Analytics flow
    PushHandler --> DeliveryTracker
    EmailHandler --> DeliveryTracker
    SMSHandler --> DeliveryTracker
    InAppHandler --> DeliveryTracker

    DeliveryTracker --> AnalyticsService
    AnalyticsService --> MetricsCollector
    AnalyticsService --> ReportingService
    ABTestingService --> NotificationProcessor

    %% Infrastructure
    NotificationProcessor --> ConfigService
    NotificationAPI --> HealthChecker
    TemplateEngine --> LogAggregator
    MetricsCollector --> AlertManager
    NotificationDB --> BackupService

    %% Failure handling
    PushHandler --> RetryQueue
    EmailHandler --> RetryQueue
    SMSHandler --> RetryQueue
    RetryQueue --> DeadLetterQueue

    classDef client fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef gateway fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef api fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef processing fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef queue fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef handler fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef external fill:#e0f2f1,stroke:#00796b,stroke-width:2px
    classDef storage fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
    classDef analytics fill:#fff8e1,stroke:#ffa000,stroke-width:2px
    classDef infrastructure fill:#ffebee,stroke:#d32f2f,stroke-width:2px

    class WebApp,MobileApp,BackendServices,ThirdPartyAPI client
    class APIGateway,LoadBalancer,RateLimiter gateway
    class NotificationAPI,TemplateAPI,PreferencesAPI,AnalyticsAPI,WebhookAPI api
    class NotificationProcessor,TemplateEngine,SegmentationService,SchedulingService,PersonalizationService processing
    class PriorityQueue,DelayedQueue,RetryQueue,DeadLetterQueue,BulkQueue queue
    class PushHandler,EmailHandler,SMSHandler,InAppHandler,WebhookHandler,SlackHandler handler
    class FCM,APNS,EmailProviders,SMSProviders,SlackAPI,WebPushService external
    class NotificationDB,TemplateDB,UserPrefsDB,AnalyticsDB,DeliveryLogDB,Cache storage
    class DeliveryTracker,AnalyticsService,MetricsCollector,ReportingService,ABTestingService analytics
    class ConfigService,HealthChecker,LogAggregator,AlertManager,BackupService infrastructure
```

## 🔧 Detailed Design

### 1. Notification API

```java
@RestController
@RequestMapping("/api/v1/notifications")
public class NotificationController {
    
    private final NotificationService notificationService;
    private final TemplateService templateService;
    
    @PostMapping("/send")
    public ResponseEntity<NotificationResponse> sendNotification(
            @RequestBody NotificationRequest request) {
        
        try {
            // Validate request
            validateNotificationRequest(request);
            
            // Process notification
            NotificationResult result = notificationService.sendNotification(request);
            
            return ResponseEntity.ok(NotificationResponse.success(result.getNotificationId()));
            
        } catch (ValidationException e) {
            return ResponseEntity.badRequest()
                .body(NotificationResponse.error(e.getMessage()));
        }
    }
    
    @PostMapping("/send-bulk")
    public ResponseEntity<BulkNotificationResponse> sendBulkNotification(
            @RequestBody BulkNotificationRequest request) {
        
        try {
            BulkNotificationResult result = notificationService.sendBulkNotification(request);
            return ResponseEntity.ok(BulkNotificationResponse.from(result));
            
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(BulkNotificationResponse.error(e.getMessage()));
        }
    }
    
    @PostMapping("/schedule")
    public ResponseEntity<NotificationResponse> scheduleNotification(
            @RequestBody ScheduledNotificationRequest request) {
        
        NotificationResult result = notificationService.scheduleNotification(request);
        return ResponseEntity.ok(NotificationResponse.success(result.getNotificationId()));
    }
}

@Service
public class NotificationService {
    
    private final NotificationProcessor notificationProcessor;
    private final TemplateService templateService;
    private final UserPreferencesService userPreferencesService;
    private final NotificationRepository notificationRepository;
    
    public NotificationResult sendNotification(NotificationRequest request) {
        // Create notification record
        Notification notification = createNotification(request);
        
        // Check user preferences
        if (!shouldSendNotification(notification)) {
            notification.setStatus(NotificationStatus.SKIPPED);
            notificationRepository.save(notification);
            return NotificationResult.skipped(notification.getId());
        }
        
        // Render template if specified
        if (request.getTemplateId() != null) {
            String renderedContent = templateService.render(
                request.getTemplateId(), 
                request.getTemplateData()
            );
            notification.setContent(renderedContent);
        }
        
        // Save notification
        notification = notificationRepository.save(notification);
        
        // Queue for processing
        notificationProcessor.process(notification);
        
        return NotificationResult.success(notification.getId());
    }
    
    public BulkNotificationResult sendBulkNotification(BulkNotificationRequest request) {
        List<String> userIds = resolveTargetUsers(request.getTargeting());
        List<CompletableFuture<NotificationResult>> futures = new ArrayList<>();
        
        for (String userId : userIds) {
            NotificationRequest individualRequest = NotificationRequest.builder()
                .userId(userId)
                .channel(request.getChannel())
                .templateId(request.getTemplateId())
                .templateData(request.getTemplateData())
                .priority(request.getPriority())
                .build();
            
            CompletableFuture<NotificationResult> future = CompletableFuture
                .supplyAsync(() -> sendNotification(individualRequest));
            futures.add(future);
        }
        
        // Wait for all notifications to be queued
        List<NotificationResult> results = futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toList());
        
        return BulkNotificationResult.from(results);
    }
    
    private boolean shouldSendNotification(Notification notification) {
        UserPreferences preferences = userPreferencesService
            .getPreferences(notification.getUserId());
        
        // Check if user has opted out of this channel
        if (!preferences.isChannelEnabled(notification.getChannel())) {
            return false;
        }
        
        // Check quiet hours
        if (preferences.isInQuietHours(notification.getChannel())) {
            return false;
        }
        
        // Check frequency limits
        if (preferences.exceedsFrequencyLimit(notification.getChannel())) {
            return false;
        }
        
        return true;
    }
}
```

### 2. Template Engine

```java
@Service
public class TemplateService {
    
    private final TemplateRepository templateRepository;
    private final TemplateEngine templateEngine;
    private final Cache<String, Template> templateCache;
    
    public String render(String templateId, Map<String, Object> data) {
        Template template = getTemplate(templateId);
        
        try {
            return templateEngine.render(template.getContent(), data);
        } catch (TemplateException e) {
            throw new NotificationException("Failed to render template: " + templateId, e);
        }
    }
    
    private Template getTemplate(String templateId) {
        // Try cache first
        Template template = templateCache.getIfPresent(templateId);
        if (template != null) {
            return template;
        }
        
        // Load from database
        template = templateRepository.findById(templateId)
            .orElseThrow(() -> new TemplateNotFoundException(templateId));
        
        // Cache for future use
        templateCache.put(templateId, template);
        
        return template;
    }
    
    public Template createTemplate(CreateTemplateRequest request) {
        Template template = Template.builder()
            .id(generateTemplateId())
            .name(request.getName())
            .channel(request.getChannel())
            .subject(request.getSubject())
            .content(request.getContent())
            .variables(extractVariables(request.getContent()))
            .createdAt(Instant.now())
            .build();
        
        // Validate template syntax
        validateTemplate(template);
        
        return templateRepository.save(template);
    }
    
    private void validateTemplate(Template template) {
        try {
            // Test render with dummy data
            Map<String, Object> testData = createTestData(template.getVariables());
            templateEngine.render(template.getContent(), testData);
        } catch (Exception e) {
            throw new InvalidTemplateException("Template validation failed", e);
        }
    }
}

@Component
public class HandlebarsTemplateEngine implements TemplateEngine {
    
    private final Handlebars handlebars;
    
    public HandlebarsTemplateEngine() {
        this.handlebars = new Handlebars();
        registerHelpers();
    }
    
    @Override
    public String render(String templateContent, Map<String, Object> data) {
        try {
            com.github.jknack.handlebars.Template template = handlebars.compileInline(templateContent);
            return template.apply(data);
        } catch (IOException e) {
            throw new TemplateRenderException("Failed to render template", e);
        }
    }
    
    private void registerHelpers() {
        // Date formatting helper
        handlebars.registerHelper("formatDate", (context, options) -> {
            if (context instanceof Instant) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                return ((Instant) context).atZone(ZoneId.systemDefault()).format(formatter);
            }
            return context.toString();
        });
        
        // Currency formatting helper
        handlebars.registerHelper("formatCurrency", (context, options) -> {
            if (context instanceof Number) {
                return NumberFormat.getCurrencyInstance().format(context);
            }
            return context.toString();
        });
    }
}
```

### 3. Channel Handlers

```java
@Component
public class PushNotificationHandler implements NotificationHandler {
    
    private final FCMService fcmService;
    private final APNSService apnsService;
    private final DeviceTokenService deviceTokenService;
    
    @Override
    public boolean canHandle(NotificationChannel channel) {
        return channel == NotificationChannel.PUSH;
    }
    
    @Override
    public DeliveryResult handle(Notification notification) {
        try {
            List<String> deviceTokens = deviceTokenService.getDeviceTokens(notification.getUserId());
            
            if (deviceTokens.isEmpty()) {
                return DeliveryResult.failed("No device tokens found");
            }
            
            List<DeliveryResult> results = new ArrayList<>();
            
            for (String deviceToken : deviceTokens) {
                DeviceType deviceType = deviceTokenService.getDeviceType(deviceToken);
                
                DeliveryResult result = switch (deviceType) {
                    case ANDROID -> sendToAndroid(notification, deviceToken);
                    case IOS -> sendToIOS(notification, deviceToken);
                    default -> DeliveryResult.failed("Unsupported device type");
                };
                
                results.add(result);
            }
            
            // Return success if at least one delivery succeeded
            boolean anySuccess = results.stream().anyMatch(DeliveryResult::isSuccess);
            return anySuccess ? DeliveryResult.success() : DeliveryResult.failed("All deliveries failed");
            
        } catch (Exception e) {
            return DeliveryResult.failed("Push notification failed: " + e.getMessage());
        }
    }
    
    private DeliveryResult sendToAndroid(Notification notification, String deviceToken) {
        try {
            Message message = Message.builder()
                .setToken(deviceToken)
                .setNotification(com.google.firebase.messaging.Notification.builder()
                    .setTitle(notification.getTitle())
                    .setBody(notification.getContent())
                    .build())
                .putAllData(notification.getData())
                .build();
            
            String messageId = fcmService.send(message);
            return DeliveryResult.success(messageId);
            
        } catch (FirebaseMessagingException e) {
            if (e.getErrorCode() == "UNREGISTERED") {
                // Remove invalid token
                deviceTokenService.removeDeviceToken(deviceToken);
            }
            return DeliveryResult.failed(e.getMessage());
        }
    }
    
    private DeliveryResult sendToIOS(Notification notification, String deviceToken) {
        try {
            ApnsPayloadBuilder payloadBuilder = new ApnsPayloadBuilder();
            payloadBuilder.setAlertTitle(notification.getTitle());
            payloadBuilder.setAlertBody(notification.getContent());
            payloadBuilder.setBadgeNumber(1);
            payloadBuilder.setSound("default");
            
            // Add custom data
            for (Map.Entry<String, String> entry : notification.getData().entrySet()) {
                payloadBuilder.addCustomProperty(entry.getKey(), entry.getValue());
            }
            
            String payload = payloadBuilder.buildWithDefaultMaximumLength();
            
            SimpleApnsPushNotification pushNotification = new SimpleApnsPushNotification(
                deviceToken, 
                notification.getTopicId(), 
                payload
            );
            
            PushNotificationResponse<SimpleApnsPushNotification> response = 
                apnsService.sendNotification(pushNotification).get();
            
            if (response.isAccepted()) {
                return DeliveryResult.success(response.getApnsId().toString());
            } else {
                return DeliveryResult.failed(response.getRejectionReason());
            }
            
        } catch (Exception e) {
            return DeliveryResult.failed(e.getMessage());
        }
    }
}

@Component
public class EmailNotificationHandler implements NotificationHandler {
    
    private final EmailService emailService;
    private final UserService userService;
    
    @Override
    public boolean canHandle(NotificationChannel channel) {
        return channel == NotificationChannel.EMAIL;
    }
    
    @Override
    public DeliveryResult handle(Notification notification) {
        try {
            User user = userService.getUser(notification.getUserId());
            
            if (user.getEmail() == null) {
                return DeliveryResult.failed("User email not found");
            }
            
            EmailMessage email = EmailMessage.builder()
                .to(user.getEmail())
                .subject(notification.getTitle())
                .htmlBody(notification.getContent())
                .from("<EMAIL>")
                .build();
            
            String messageId = emailService.send(email);
            return DeliveryResult.success(messageId);
            
        } catch (Exception e) {
            return DeliveryResult.failed("Email delivery failed: " + e.getMessage());
        }
    }
}
```

### 4. Rate Limiting and Throttling

```java
@Component
public class NotificationRateLimiter {
    
    private final RedisTemplate<String, String> redisTemplate;
    private final Map<NotificationChannel, ChannelLimits> channelLimits;
    
    public boolean isAllowed(String userId, NotificationChannel channel) {
        ChannelLimits limits = channelLimits.get(channel);
        if (limits == null) {
            return true;
        }
        
        // Check per-minute limit
        String minuteKey = String.format("rate_limit:%s:%s:minute:%d", 
            userId, channel, System.currentTimeMillis() / 60000);
        
        Long minuteCount = redisTemplate.opsForValue().increment(minuteKey);
        redisTemplate.expire(minuteKey, Duration.ofMinutes(1));
        
        if (minuteCount > limits.getPerMinute()) {
            return false;
        }
        
        // Check per-hour limit
        String hourKey = String.format("rate_limit:%s:%s:hour:%d", 
            userId, channel, System.currentTimeMillis() / 3600000);
        
        Long hourCount = redisTemplate.opsForValue().increment(hourKey);
        redisTemplate.expire(hourKey, Duration.ofHours(1));
        
        if (hourCount > limits.getPerHour()) {
            return false;
        }
        
        // Check per-day limit
        String dayKey = String.format("rate_limit:%s:%s:day:%d", 
            userId, channel, System.currentTimeMillis() / 86400000);
        
        Long dayCount = redisTemplate.opsForValue().increment(dayKey);
        redisTemplate.expire(dayKey, Duration.ofDays(1));
        
        return dayCount <= limits.getPerDay();
    }
}

@Component
public class NotificationProcessor {
    
    private final List<NotificationHandler> handlers;
    private final NotificationRateLimiter rateLimiter;
    private final DeliveryTracker deliveryTracker;
    private final NotificationRepository notificationRepository;
    
    @EventListener
    public void process(Notification notification) {
        try {
            // Check rate limits
            if (!rateLimiter.isAllowed(notification.getUserId(), notification.getChannel())) {
                notification.setStatus(NotificationStatus.RATE_LIMITED);
                notificationRepository.save(notification);
                return;
            }
            
            // Find appropriate handler
            NotificationHandler handler = handlers.stream()
                .filter(h -> h.canHandle(notification.getChannel()))
                .findFirst()
                .orElseThrow(() -> new UnsupportedChannelException(notification.getChannel()));
            
            // Update status to processing
            notification.setStatus(NotificationStatus.PROCESSING);
            notification.setProcessedAt(Instant.now());
            notificationRepository.save(notification);
            
            // Handle delivery
            DeliveryResult result = handler.handle(notification);
            
            // Update status based on result
            if (result.isSuccess()) {
                notification.setStatus(NotificationStatus.DELIVERED);
                notification.setDeliveredAt(Instant.now());
                notification.setExternalId(result.getExternalId());
            } else {
                notification.setStatus(NotificationStatus.FAILED);
                notification.setErrorMessage(result.getErrorMessage());
                
                // Retry logic
                if (notification.getRetryCount() < MAX_RETRIES) {
                    scheduleRetry(notification);
                }
            }
            
            notificationRepository.save(notification);
            
            // Track delivery metrics
            deliveryTracker.track(notification, result);
            
        } catch (Exception e) {
            handleProcessingError(notification, e);
        }
    }
    
    private void scheduleRetry(Notification notification) {
        notification.setRetryCount(notification.getRetryCount() + 1);
        
        // Exponential backoff
        long delayMinutes = (long) Math.pow(2, notification.getRetryCount());
        Instant retryAt = Instant.now().plus(delayMinutes, ChronoUnit.MINUTES);
        
        notification.setScheduledAt(retryAt);
        notification.setStatus(NotificationStatus.SCHEDULED);
    }
}
```

### 5. Analytics and Monitoring

```java
@Service
public class NotificationAnalyticsService {
    
    private final AnalyticsRepository analyticsRepository;
    private final MeterRegistry meterRegistry;
    
    public void recordDelivery(Notification notification, DeliveryResult result) {
        // Record metrics
        Counter.builder("notifications.delivered")
            .tag("channel", notification.getChannel().name())
            .tag("status", result.isSuccess() ? "success" : "failed")
            .register(meterRegistry)
            .increment();
        
        // Record delivery time
        if (notification.getProcessedAt() != null && notification.getDeliveredAt() != null) {
            long deliveryTimeMs = Duration.between(
                notification.getProcessedAt(), 
                notification.getDeliveredAt()
            ).toMillis();
            
            Timer.builder("notifications.delivery.time")
                .tag("channel", notification.getChannel().name())
                .register(meterRegistry)
                .record(deliveryTimeMs, TimeUnit.MILLISECONDS);
        }
        
        // Store detailed analytics
        NotificationAnalytics analytics = NotificationAnalytics.builder()
            .notificationId(notification.getId())
            .userId(notification.getUserId())
            .channel(notification.getChannel())
            .status(result.isSuccess() ? "delivered" : "failed")
            .deliveryTime(notification.getDeliveredAt())
            .errorMessage(result.getErrorMessage())
            .build();
        
        analyticsRepository.save(analytics);
    }
    
    public DeliveryReport generateDeliveryReport(String userId, Instant from, Instant to) {
        List<NotificationAnalytics> analytics = analyticsRepository
            .findByUserIdAndDeliveryTimeBetween(userId, from, to);
        
        Map<NotificationChannel, Long> deliveryByChannel = analytics.stream()
            .collect(Collectors.groupingBy(
                NotificationAnalytics::getChannel,
                Collectors.counting()
            ));
        
        long totalDelivered = analytics.stream()
            .filter(a -> "delivered".equals(a.getStatus()))
            .count();
        
        long totalFailed = analytics.stream()
            .filter(a -> "failed".equals(a.getStatus()))
            .count();
        
        double deliveryRate = (double) totalDelivered / (totalDelivered + totalFailed) * 100;
        
        return DeliveryReport.builder()
            .userId(userId)
            .period(from + " to " + to)
            .totalNotifications(analytics.size())
            .deliveredCount(totalDelivered)
            .failedCount(totalFailed)
            .deliveryRate(deliveryRate)
            .deliveryByChannel(deliveryByChannel)
            .build();
    }
}
```

This notification system design provides a scalable, multi-channel solution capable of sending millions of notifications per day with high delivery rates and comprehensive analytics.
