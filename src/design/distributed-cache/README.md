# Distributed Cache System Design

Design a distributed caching system like Redis Cluster, Memcached, or Amazon ElastiCache that provides high-performance, scalable caching across multiple nodes.

## 📋 Requirements

### Functional Requirements
1. **Get/Put Operations**: Store and retrieve key-value pairs
2. **Distributed Storage**: Data distributed across multiple nodes
3. **Replication**: Data replicated for fault tolerance
4. **Consistency**: Configurable consistency levels
5. **Expiration**: TTL (Time To Live) support for keys
6. **Eviction Policies**: LRU, LFU, FIFO eviction when memory is full

### Non-Functional Requirements
1. **Scale**: Handle 1M requests per second
2. **Latency**: < 1ms average response time
3. **Availability**: 99.99% uptime
4. **Memory**: Support TB-scale data storage
5. **Consistency**: Eventually consistent with configurable options

## 🔢 Capacity Estimation

### Traffic Estimates
- **Peak requests**: 1M requests/second
- **Read:Write ratio**: 80:20
- **Average key size**: 250 bytes
- **Average value size**: 1KB

### Memory Estimates
- **Total keys**: 1B keys
- **Memory per key-value**: ~1.25KB
- **Total memory**: 1B × 1.25KB = ~1.25TB
- **Replication factor**: 3x = ~3.75TB total

### Network Estimates
- **Peak bandwidth**: 1M req/s × 1.25KB = ~1.25GB/s
- **Replication traffic**: Additional 500MB/s

## 🏗️ High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        Client1[Client 1]
        Client2[Client 2]
        ClientN[Client N]
    end
    
    subgraph "Load Balancer"
        LB[Consistent Hash Load Balancer]
    end
    
    subgraph "Cache Cluster"
        subgraph "Shard 1"
            Master1[Master Node 1]
            Replica1A[Replica 1A]
            Replica1B[Replica 1B]
        end
        
        subgraph "Shard 2"
            Master2[Master Node 2]
            Replica2A[Replica 2A]
            Replica2B[Replica 2B]
        end
        
        subgraph "Shard 3"
            Master3[Master Node 3]
            Replica3A[Replica 3A]
            Replica3B[Replica 3B]
        end
    end
    
    subgraph "Management Layer"
        ConfigService[Configuration Service]
        MonitoringService[Monitoring Service]
        HealthChecker[Health Checker]
    end
    
    Client1 --> LB
    Client2 --> LB
    ClientN --> LB
    
    LB --> Master1
    LB --> Master2
    LB --> Master3
    
    Master1 --> Replica1A
    Master1 --> Replica1B
    Master2 --> Replica2A
    Master2 --> Replica2B
    Master3 --> Replica3A
    Master3 --> Replica3B
    
    ConfigService --> Master1
    ConfigService --> Master2
    ConfigService --> Master3
    
    MonitoringService --> Master1
    MonitoringService --> Master2
    MonitoringService --> Master3
```

## 🔧 Detailed Design

### 1. Consistent Hashing Implementation

```java
public class ConsistentHashRing {
    private final TreeMap<Long, CacheNode> ring = new TreeMap<>();
    private final int virtualNodes;
    private final MessageDigest md5;
    
    public ConsistentHashRing(int virtualNodes) {
        this.virtualNodes = virtualNodes;
        try {
            this.md5 = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5 not available", e);
        }
    }
    
    public void addNode(CacheNode node) {
        for (int i = 0; i < virtualNodes; i++) {
            String virtualNodeKey = node.getNodeId() + ":" + i;
            long hash = hash(virtualNodeKey);
            ring.put(hash, node);
        }
    }
    
    public void removeNode(CacheNode node) {
        for (int i = 0; i < virtualNodes; i++) {
            String virtualNodeKey = node.getNodeId() + ":" + i;
            long hash = hash(virtualNodeKey);
            ring.remove(hash);
        }
    }
    
    public CacheNode getNode(String key) {
        if (ring.isEmpty()) {
            return null;
        }
        
        long hash = hash(key);
        Map.Entry<Long, CacheNode> entry = ring.ceilingEntry(hash);
        
        if (entry == null) {
            entry = ring.firstEntry();
        }
        
        return entry.getValue();
    }
    
    private long hash(String key) {
        md5.reset();
        md5.update(key.getBytes());
        byte[] digest = md5.digest();
        
        long hash = 0;
        for (int i = 0; i < 8; i++) {
            hash = (hash << 8) | (digest[i] & 0xFF);
        }
        
        return hash;
    }
}
```

### 2. Cache Node Implementation

```java
public class CacheNode {
    private final String nodeId;
    private final ConcurrentHashMap<String, CacheEntry> cache;
    private final ScheduledExecutorService cleanupExecutor;
    private final AtomicLong memoryUsage;
    private final long maxMemory;
    
    public CacheNode(String nodeId, long maxMemory) {
        this.nodeId = nodeId;
        this.maxMemory = maxMemory;
        this.cache = new ConcurrentHashMap<>();
        this.memoryUsage = new AtomicLong(0);
        this.cleanupExecutor = Executors.newSingleThreadScheduledExecutor();
        
        // Schedule periodic cleanup of expired entries
        cleanupExecutor.scheduleAtFixedRate(this::cleanupExpiredEntries, 1, 1, TimeUnit.MINUTES);
    }
    
    public boolean put(String key, byte[] value, long ttlMs) {
        long expirationTime = ttlMs > 0 ? System.currentTimeMillis() + ttlMs : Long.MAX_VALUE;
        CacheEntry entry = new CacheEntry(value, expirationTime, System.currentTimeMillis());
        
        // Check memory constraints
        long entrySize = calculateEntrySize(key, entry);
        if (memoryUsage.get() + entrySize > maxMemory) {
            evictLRUEntries(entrySize);
        }
        
        CacheEntry oldEntry = cache.put(key, entry);
        
        // Update memory usage
        if (oldEntry != null) {
            memoryUsage.addAndGet(-calculateEntrySize(key, oldEntry));
        }
        memoryUsage.addAndGet(entrySize);
        
        return true;
    }
    
    public byte[] get(String key) {
        CacheEntry entry = cache.get(key);
        
        if (entry == null) {
            return null;
        }
        
        // Check expiration
        if (entry.isExpired()) {
            cache.remove(key);
            memoryUsage.addAndGet(-calculateEntrySize(key, entry));
            return null;
        }
        
        // Update access time for LRU
        entry.updateAccessTime();
        return entry.getValue();
    }
    
    public boolean delete(String key) {
        CacheEntry entry = cache.remove(key);
        if (entry != null) {
            memoryUsage.addAndGet(-calculateEntrySize(key, entry));
            return true;
        }
        return false;
    }
    
    private void evictLRUEntries(long requiredSpace) {
        List<Map.Entry<String, CacheEntry>> entries = cache.entrySet()
            .stream()
            .sorted((e1, e2) -> Long.compare(e1.getValue().getLastAccessTime(), 
                                           e2.getValue().getLastAccessTime()))
            .collect(Collectors.toList());
        
        long freedSpace = 0;
        for (Map.Entry<String, CacheEntry> entry : entries) {
            if (freedSpace >= requiredSpace) {
                break;
            }
            
            String key = entry.getKey();
            CacheEntry cacheEntry = entry.getValue();
            
            cache.remove(key);
            long entrySize = calculateEntrySize(key, cacheEntry);
            memoryUsage.addAndGet(-entrySize);
            freedSpace += entrySize;
        }
    }
    
    private void cleanupExpiredEntries() {
        long currentTime = System.currentTimeMillis();
        
        cache.entrySet().removeIf(entry -> {
            if (entry.getValue().isExpired(currentTime)) {
                memoryUsage.addAndGet(-calculateEntrySize(entry.getKey(), entry.getValue()));
                return true;
            }
            return false;
        });
    }
    
    private long calculateEntrySize(String key, CacheEntry entry) {
        return key.getBytes().length + entry.getValue().length + 64; // 64 bytes overhead
    }
}
```

### 3. Cache Entry with TTL

```java
public class CacheEntry {
    private final byte[] value;
    private final long expirationTime;
    private volatile long lastAccessTime;
    
    public CacheEntry(byte[] value, long expirationTime, long creationTime) {
        this.value = value;
        this.expirationTime = expirationTime;
        this.lastAccessTime = creationTime;
    }
    
    public boolean isExpired() {
        return isExpired(System.currentTimeMillis());
    }
    
    public boolean isExpired(long currentTime) {
        return expirationTime != Long.MAX_VALUE && currentTime > expirationTime;
    }
    
    public void updateAccessTime() {
        this.lastAccessTime = System.currentTimeMillis();
    }
    
    // Getters
    public byte[] getValue() { return value; }
    public long getExpirationTime() { return expirationTime; }
    public long getLastAccessTime() { return lastAccessTime; }
}
```

### 4. Replication Strategy

```java
public class ReplicationManager {
    private final int replicationFactor;
    private final ConsistentHashRing hashRing;
    private final ExecutorService replicationExecutor;
    
    public ReplicationManager(int replicationFactor, ConsistentHashRing hashRing) {
        this.replicationFactor = replicationFactor;
        this.hashRing = hashRing;
        this.replicationExecutor = Executors.newFixedThreadPool(10);
    }
    
    public void replicateWrite(String key, byte[] value, long ttl) {
        List<CacheNode> replicas = getReplicaNodes(key);
        
        // Async replication to replica nodes
        for (int i = 1; i < replicas.size(); i++) {
            CacheNode replica = replicas.get(i);
            replicationExecutor.submit(() -> {
                try {
                    replica.put(key, value, ttl);
                } catch (Exception e) {
                    handleReplicationFailure(replica, key, e);
                }
            });
        }
    }
    
    public byte[] replicatedRead(String key, ReadConsistency consistency) {
        List<CacheNode> replicas = getReplicaNodes(key);
        
        switch (consistency) {
            case ONE:
                return replicas.get(0).get(key);
                
            case QUORUM:
                return readQuorum(key, replicas);
                
            case ALL:
                return readAll(key, replicas);
                
            default:
                return replicas.get(0).get(key);
        }
    }
    
    private List<CacheNode> getReplicaNodes(String key) {
        List<CacheNode> replicas = new ArrayList<>();
        CacheNode primary = hashRing.getNode(key);
        replicas.add(primary);
        
        // Get next N-1 nodes in the ring for replication
        String currentKey = key;
        for (int i = 1; i < replicationFactor; i++) {
            currentKey = currentKey + ":" + i;
            CacheNode replica = hashRing.getNode(currentKey);
            if (!replicas.contains(replica)) {
                replicas.add(replica);
            }
        }
        
        return replicas;
    }
    
    private byte[] readQuorum(String key, List<CacheNode> replicas) {
        int quorumSize = (replicas.size() / 2) + 1;
        List<CompletableFuture<byte[]>> futures = new ArrayList<>();
        
        for (CacheNode replica : replicas) {
            CompletableFuture<byte[]> future = CompletableFuture.supplyAsync(() -> replica.get(key));
            futures.add(future);
        }
        
        // Wait for quorum responses
        int successCount = 0;
        byte[] result = null;
        
        for (CompletableFuture<byte[]> future : futures) {
            try {
                byte[] value = future.get(100, TimeUnit.MILLISECONDS);
                if (value != null) {
                    result = value;
                    successCount++;
                    if (successCount >= quorumSize) {
                        break;
                    }
                }
            } catch (Exception e) {
                // Continue with other replicas
            }
        }
        
        return successCount >= quorumSize ? result : null;
    }
}
```

### 5. Client Library

```java
public class DistributedCacheClient {
    private final ConsistentHashRing hashRing;
    private final ReplicationManager replicationManager;
    private final ConnectionPool connectionPool;
    
    public DistributedCacheClient(List<String> cacheNodes) {
        this.hashRing = new ConsistentHashRing(150); // 150 virtual nodes per physical node
        this.connectionPool = new ConnectionPool();
        
        // Initialize cache nodes
        for (String nodeAddress : cacheNodes) {
            CacheNode node = new CacheNode(nodeAddress, 1024 * 1024 * 1024); // 1GB per node
            hashRing.addNode(node);
        }
        
        this.replicationManager = new ReplicationManager(3, hashRing);
    }
    
    public boolean put(String key, byte[] value) {
        return put(key, value, 0); // No expiration
    }
    
    public boolean put(String key, byte[] value, long ttlSeconds) {
        try {
            CacheNode primaryNode = hashRing.getNode(key);
            boolean success = primaryNode.put(key, value, ttlSeconds * 1000);
            
            if (success) {
                // Async replication
                replicationManager.replicateWrite(key, value, ttlSeconds * 1000);
            }
            
            return success;
        } catch (Exception e) {
            handleException("PUT", key, e);
            return false;
        }
    }
    
    public byte[] get(String key) {
        return get(key, ReadConsistency.ONE);
    }
    
    public byte[] get(String key, ReadConsistency consistency) {
        try {
            return replicationManager.replicatedRead(key, consistency);
        } catch (Exception e) {
            handleException("GET", key, e);
            return null;
        }
    }
    
    public boolean delete(String key) {
        try {
            List<CacheNode> replicas = replicationManager.getReplicaNodes(key);
            boolean success = false;
            
            for (CacheNode replica : replicas) {
                if (replica.delete(key)) {
                    success = true;
                }
            }
            
            return success;
        } catch (Exception e) {
            handleException("DELETE", key, e);
            return false;
        }
    }
}
```

## ⚡ Advanced Features

### 1. Hot Key Detection

```java
public class HotKeyDetector {
    private final ConcurrentHashMap<String, AtomicLong> keyAccessCount;
    private final ScheduledExecutorService scheduler;
    private final int hotKeyThreshold;
    
    public HotKeyDetector(int hotKeyThreshold) {
        this.hotKeyThreshold = hotKeyThreshold;
        this.keyAccessCount = new ConcurrentHashMap<>();
        this.scheduler = Executors.newSingleThreadScheduledExecutor();
        
        // Reset counters every minute
        scheduler.scheduleAtFixedRate(this::resetCounters, 1, 1, TimeUnit.MINUTES);
    }
    
    public void recordAccess(String key) {
        keyAccessCount.computeIfAbsent(key, k -> new AtomicLong(0)).incrementAndGet();
    }
    
    public Set<String> getHotKeys() {
        return keyAccessCount.entrySet().stream()
            .filter(entry -> entry.getValue().get() > hotKeyThreshold)
            .map(Map.Entry::getKey)
            .collect(Collectors.toSet());
    }
    
    private void resetCounters() {
        keyAccessCount.clear();
    }
}
```

### 2. Cache Warming

```java
public class CacheWarmer {
    private final DistributedCacheClient cacheClient;
    private final DataSource dataSource;
    
    public void warmCache(List<String> popularKeys) {
        ExecutorService executor = Executors.newFixedThreadPool(10);
        
        List<CompletableFuture<Void>> futures = popularKeys.stream()
            .map(key -> CompletableFuture.runAsync(() -> warmKey(key), executor))
            .collect(Collectors.toList());
        
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        executor.shutdown();
    }
    
    private void warmKey(String key) {
        try {
            // Check if key exists in cache
            if (cacheClient.get(key) == null) {
                // Load from data source
                byte[] value = dataSource.load(key);
                if (value != null) {
                    cacheClient.put(key, value, 3600); // 1 hour TTL
                }
            }
        } catch (Exception e) {
            // Log error but continue warming other keys
        }
    }
}
```

## 📊 Monitoring and Metrics

```java
public class CacheMetrics {
    private final MeterRegistry meterRegistry;
    private final Counter hitCounter;
    private final Counter missCounter;
    private final Timer getTimer;
    private final Timer putTimer;
    private final Gauge memoryUsageGauge;
    
    public CacheMetrics(MeterRegistry meterRegistry, CacheNode cacheNode) {
        this.meterRegistry = meterRegistry;
        this.hitCounter = Counter.builder("cache.hits").register(meterRegistry);
        this.missCounter = Counter.builder("cache.misses").register(meterRegistry);
        this.getTimer = Timer.builder("cache.get.duration").register(meterRegistry);
        this.putTimer = Timer.builder("cache.put.duration").register(meterRegistry);
        this.memoryUsageGauge = Gauge.builder("cache.memory.usage")
            .register(meterRegistry, cacheNode, node -> node.getMemoryUsage());
    }
    
    public void recordHit() {
        hitCounter.increment();
    }
    
    public void recordMiss() {
        missCounter.increment();
    }
    
    public Timer.Sample startGetTimer() {
        return Timer.start(meterRegistry);
    }
    
    public Timer.Sample startPutTimer() {
        return Timer.start(meterRegistry);
    }
}
```

This distributed cache design provides a highly scalable, fault-tolerant caching solution that can handle millions of requests per second with sub-millisecond latency.
