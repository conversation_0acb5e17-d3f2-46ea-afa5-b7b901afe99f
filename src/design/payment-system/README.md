# Payment Processing System Design

Design a secure, scalable payment processing system like Stripe, PayPal, or Square that handles financial transactions, supports multiple payment methods, and ensures PCI compliance.

## 📋 Requirements

### Functional Requirements
1. **Payment Processing**: Process credit/debit card payments
2. **Multiple Payment Methods**: Cards, digital wallets, bank transfers
3. **Multi-currency Support**: Handle different currencies and exchange rates
4. **Recurring Payments**: Support subscriptions and recurring billing
5. **Refunds & Disputes**: Handle refunds, chargebacks, and disputes
6. **Fraud Detection**: Real-time fraud detection and prevention
7. **Reporting**: Transaction reporting and analytics

### Non-Functional Requirements
1. **Scale**: Handle 1M transactions per day
2. **Availability**: 99.99% uptime (4 minutes downtime/month)
3. **Latency**: < 500ms payment processing time
4. **Security**: PCI DSS compliance, data encryption
5. **Consistency**: Strong consistency for financial data
6. **Reliability**: Zero data loss, exactly-once processing

## 🔢 Capacity Estimation

### Transaction Volume
- **Daily transactions**: 1M transactions/day
- **Peak TPS**: ~50 transactions/second (assuming 5x average)
- **Average transaction**: $50
- **Daily volume**: $50M/day

### Storage Estimates
- **Transaction record**: ~1KB per transaction
- **Daily storage**: 1M × 1KB = 1GB/day
- **Annual storage**: ~365GB/year
- **With replication**: ~1TB/year

### Network Estimates
- **API calls**: 3x transaction volume (auth, capture, webhook)
- **Peak API calls**: 150 requests/second
- **Bandwidth**: ~150KB/second

## 🏗️ High-Level Architecture

```mermaid
graph TB
    subgraph "Client Applications"
        WebApp[Web Applications<br/>E-commerce Sites]
        MobileApp[Mobile Apps<br/>iOS/Android]
        POS[Point of Sale<br/>Physical Terminals]
        APIClients[API Clients<br/>Third-party Integrations]
    end

    subgraph "Security & Gateway Layer"
        WAF[Web Application Firewall<br/>DDoS Protection]
        APIGateway[API Gateway<br/>SSL Termination]
        RateLimiter[Rate Limiter<br/>Abuse Prevention]
        Authentication[Authentication Service<br/>OAuth 2.0/JWT]
    end

    subgraph "Core Payment Services"
        PaymentAPI[Payment API<br/>RESTful Interface]
        PaymentProcessor[Payment Processor<br/>Transaction Orchestration]
        PaymentMethodService[Payment Method Service<br/>Card/Wallet Management]
        TokenizationService[Tokenization Service<br/>PCI Compliance]
    end

    subgraph "Risk & Fraud Management"
        FraudDetection[Fraud Detection<br/>ML-based Scoring]
        RiskEngine[Risk Engine<br/>Rule-based Analysis]
        VelocityChecker[Velocity Checker<br/>Transaction Limits]
        BlacklistService[Blacklist Service<br/>Known Bad Actors]
    end

    subgraph "Core Business Services"
        AccountService[Account Service<br/>Merchant Management]
        TransactionService[Transaction Service<br/>Payment Lifecycle]
        RefundService[Refund Service<br/>Chargeback Handling]
        RecurringService[Recurring Service<br/>Subscription Billing]
        SettlementService[Settlement Service<br/>Payout Processing]
    end

    subgraph "External Payment Networks"
        CardNetworks[Card Networks<br/>Visa/Mastercard/Amex]
        AcquiringBanks[Acquiring Banks<br/>Payment Processing]
        DigitalWallets[Digital Wallets<br/>PayPal/Apple Pay/Google Pay]
        BankTransfers[Bank Transfers<br/>ACH/Wire/SEPA]
        CryptoCurrency[Cryptocurrency<br/>Bitcoin/Ethereum]
    end

    subgraph "Data Storage Layer"
        TransactionDB[(Transaction Database<br/>PostgreSQL Cluster)]
        AccountDB[(Account Database<br/>MySQL Cluster)]
        AuditDB[(Audit Database<br/>Immutable Logs)]
        VaultDB[(Vault Database<br/>Encrypted Sensitive Data)]
        Cache[Distributed Cache<br/>Redis Cluster]
        SearchIndex[(Search Index<br/>Elasticsearch)]
    end

    subgraph "Security Infrastructure"
        HSM[Hardware Security Module<br/>Key Management]
        EncryptionService[Encryption Service<br/>Data Protection]
        PKIService[PKI Service<br/>Certificate Management]
        SecretManager[Secret Manager<br/>API Keys/Passwords]
    end

    subgraph "Messaging & Events"
        MessageQueue[Message Queue<br/>Apache Kafka]
        EventBus[Event Bus<br/>Real-time Events]
        NotificationService[Notification Service<br/>Webhooks/Email/SMS]
        DeadLetterQueue[Dead Letter Queue<br/>Failed Messages]
    end

    subgraph "Analytics & Reporting"
        ReportingService[Reporting Service<br/>Financial Reports]
        AnalyticsEngine[Analytics Engine<br/>Transaction Analysis]
        DataWarehouse[(Data Warehouse<br/>BigQuery/Snowflake)]
        ComplianceReporting[Compliance Reporting<br/>Regulatory Requirements]
    end

    subgraph "External Services"
        FraudProviders[Fraud Providers<br/>Third-party ML Models]
        KYCProviders[KYC Providers<br/>Identity Verification]
        CurrencyService[Currency Service<br/>Exchange Rates]
        TaxService[Tax Service<br/>Tax Calculation]
        EmailService[Email Service<br/>Transaction Receipts]
    end

    subgraph "Monitoring & Operations"
        MetricsCollector[Metrics Collector<br/>Prometheus]
        LogAggregator[Log Aggregator<br/>ELK Stack]
        AlertManager[Alert Manager<br/>PagerDuty]
        HealthChecker[Health Checker<br/>Service Monitoring]
    end

    %% Client connections
    WebApp --> WAF
    MobileApp --> WAF
    POS --> WAF
    APIClients --> WAF

    %% Security layer
    WAF --> APIGateway
    APIGateway --> RateLimiter
    RateLimiter --> Authentication
    Authentication --> PaymentAPI

    %% Core payment flow
    PaymentAPI --> PaymentProcessor
    PaymentProcessor --> PaymentMethodService
    PaymentProcessor --> TokenizationService

    %% Risk and fraud checks
    PaymentProcessor --> FraudDetection
    PaymentProcessor --> RiskEngine
    FraudDetection --> VelocityChecker
    FraudDetection --> BlacklistService

    %% Business services
    PaymentProcessor --> TransactionService
    PaymentProcessor --> AccountService
    TransactionService --> RefundService
    TransactionService --> RecurringService
    TransactionService --> SettlementService

    %% External payment networks
    PaymentProcessor --> CardNetworks
    PaymentProcessor --> AcquiringBanks
    PaymentProcessor --> DigitalWallets
    PaymentProcessor --> BankTransfers
    PaymentProcessor --> CryptoCurrency

    %% Data storage
    TransactionService --> TransactionDB
    AccountService --> AccountDB
    PaymentProcessor --> AuditDB
    TokenizationService --> VaultDB
    PaymentAPI --> Cache
    ReportingService --> SearchIndex

    %% Security infrastructure
    TokenizationService --> HSM
    PaymentProcessor --> EncryptionService
    Authentication --> PKIService
    PaymentAPI --> SecretManager

    %% Messaging and events
    PaymentProcessor --> MessageQueue
    MessageQueue --> EventBus
    EventBus --> NotificationService
    MessageQueue --> DeadLetterQueue

    %% Analytics and reporting
    TransactionService --> ReportingService
    MessageQueue --> AnalyticsEngine
    AnalyticsEngine --> DataWarehouse
    ReportingService --> ComplianceReporting

    %% External services
    FraudDetection --> FraudProviders
    AccountService --> KYCProviders
    PaymentProcessor --> CurrencyService
    TransactionService --> TaxService
    NotificationService --> EmailService

    %% Monitoring
    PaymentProcessor --> MetricsCollector
    PaymentAPI --> LogAggregator
    MetricsCollector --> AlertManager
    HealthChecker --> AlertManager

    classDef client fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef security fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef payment fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef risk fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef business fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef external fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef storage fill:#e0f2f1,stroke:#00796b,stroke-width:2px
    classDef infrastructure fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
    classDef messaging fill:#fff8e1,stroke:#ffa000,stroke-width:2px
    classDef analytics fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef services fill:#e0f7fa,stroke:#00838f,stroke-width:2px
    classDef monitoring fill:#f9fbe7,stroke:#827717,stroke-width:2px

    class WebApp,MobileApp,POS,APIClients client
    class WAF,APIGateway,RateLimiter,Authentication security
    class PaymentAPI,PaymentProcessor,PaymentMethodService,TokenizationService payment
    class FraudDetection,RiskEngine,VelocityChecker,BlacklistService risk
    class AccountService,TransactionService,RefundService,RecurringService,SettlementService business
    class CardNetworks,AcquiringBanks,DigitalWallets,BankTransfers,CryptoCurrency external
    class TransactionDB,AccountDB,AuditDB,VaultDB,Cache,SearchIndex storage
    class HSM,EncryptionService,PKIService,SecretManager infrastructure
    class MessageQueue,EventBus,NotificationService,DeadLetterQueue messaging
    class ReportingService,AnalyticsEngine,DataWarehouse,ComplianceReporting analytics
    class FraudProviders,KYCProviders,CurrencyService,TaxService,EmailService services
    class MetricsCollector,LogAggregator,AlertManager,HealthChecker monitoring
```

## 🔧 Detailed Design

### 1. Payment Processing Flow

```java
@RestController
@RequestMapping("/api/v1/payments")
public class PaymentController {
    
    private final PaymentService paymentService;
    private final FraudDetectionService fraudService;
    private final RiskEngine riskEngine;
    
    @PostMapping("/process")
    public ResponseEntity<PaymentResponse> processPayment(@RequestBody PaymentRequest request) {
        try {
            // Validate request
            validatePaymentRequest(request);
            
            // Risk assessment
            RiskAssessment risk = riskEngine.assess(request);
            if (risk.getLevel() == RiskLevel.HIGH) {
                return ResponseEntity.badRequest()
                    .body(PaymentResponse.declined("High risk transaction"));
            }
            
            // Fraud detection
            FraudResult fraudResult = fraudService.checkFraud(request);
            if (fraudResult.isFraudulent()) {
                return ResponseEntity.badRequest()
                    .body(PaymentResponse.declined("Fraud detected"));
            }
            
            // Process payment
            PaymentResult result = paymentService.processPayment(request);
            
            // Return response
            return ResponseEntity.ok(PaymentResponse.from(result));
            
        } catch (PaymentException e) {
            return ResponseEntity.badRequest()
                .body(PaymentResponse.error(e.getMessage()));
        }
    }
}

@Service
public class PaymentService {
    
    private final TransactionService transactionService;
    private final PaymentGateway paymentGateway;
    private final AccountService accountService;
    private final NotificationService notificationService;
    
    @Transactional
    public PaymentResult processPayment(PaymentRequest request) {
        // Create transaction record
        Transaction transaction = createTransaction(request);
        
        try {
            // Authorize payment
            AuthorizationResult authResult = paymentGateway.authorize(request);
            
            if (authResult.isApproved()) {
                // Update transaction status
                transaction.setStatus(TransactionStatus.AUTHORIZED);
                transaction.setAuthorizationCode(authResult.getAuthCode());
                transactionService.save(transaction);
                
                // Capture payment (for immediate capture)
                if (request.isCaptureImmediately()) {
                    CaptureResult captureResult = paymentGateway.capture(
                        authResult.getTransactionId(), 
                        request.getAmount()
                    );
                    
                    if (captureResult.isSuccess()) {
                        transaction.setStatus(TransactionStatus.CAPTURED);
                        transactionService.save(transaction);
                        
                        // Update account balance
                        accountService.creditAccount(
                            request.getMerchantId(), 
                            request.getAmount()
                        );
                        
                        // Send notifications
                        notificationService.sendPaymentConfirmation(transaction);
                    }
                }
                
                return PaymentResult.success(transaction);
                
            } else {
                transaction.setStatus(TransactionStatus.DECLINED);
                transaction.setDeclineReason(authResult.getDeclineReason());
                transactionService.save(transaction);
                
                return PaymentResult.declined(authResult.getDeclineReason());
            }
            
        } catch (Exception e) {
            transaction.setStatus(TransactionStatus.FAILED);
            transaction.setErrorMessage(e.getMessage());
            transactionService.save(transaction);
            
            throw new PaymentProcessingException("Payment processing failed", e);
        }
    }
}
```

### 2. Transaction Management

```java
@Entity
@Table(name = "transactions")
public class Transaction {
    @Id
    private String transactionId;
    
    private String merchantId;
    private String customerId;
    private BigDecimal amount;
    private String currency;
    
    @Enumerated(EnumType.STRING)
    private TransactionStatus status;
    
    @Enumerated(EnumType.STRING)
    private PaymentMethod paymentMethod;
    
    private String authorizationCode;
    private String declineReason;
    private String errorMessage;
    
    private Instant createdAt;
    private Instant updatedAt;
    
    // Getters and setters
}

public enum TransactionStatus {
    PENDING,
    AUTHORIZED,
    CAPTURED,
    SETTLED,
    DECLINED,
    FAILED,
    REFUNDED,
    PARTIALLY_REFUNDED,
    DISPUTED
}

@Service
public class TransactionService {
    
    private final TransactionRepository transactionRepository;
    private final AuditService auditService;
    
    @Transactional
    public Transaction save(Transaction transaction) {
        transaction.setUpdatedAt(Instant.now());
        Transaction saved = transactionRepository.save(transaction);
        
        // Audit trail
        auditService.logTransactionChange(saved);
        
        return saved;
    }
    
    public Optional<Transaction> findById(String transactionId) {
        return transactionRepository.findById(transactionId);
    }
    
    public List<Transaction> findByMerchantId(String merchantId, Pageable pageable) {
        return transactionRepository.findByMerchantIdOrderByCreatedAtDesc(merchantId, pageable);
    }
    
    @Transactional
    public RefundResult refund(String transactionId, BigDecimal amount, String reason) {
        Transaction transaction = transactionRepository.findById(transactionId)
            .orElseThrow(() -> new TransactionNotFoundException(transactionId));
        
        if (!canRefund(transaction)) {
            throw new RefundNotAllowedException("Transaction cannot be refunded");
        }
        
        // Create refund transaction
        Transaction refundTransaction = Transaction.builder()
            .transactionId(generateTransactionId())
            .merchantId(transaction.getMerchantId())
            .customerId(transaction.getCustomerId())
            .amount(amount.negate()) // Negative amount for refund
            .currency(transaction.getCurrency())
            .status(TransactionStatus.PENDING)
            .paymentMethod(transaction.getPaymentMethod())
            .parentTransactionId(transactionId)
            .refundReason(reason)
            .createdAt(Instant.now())
            .build();
        
        save(refundTransaction);
        
        // Process refund with payment gateway
        // ... refund processing logic
        
        return RefundResult.success(refundTransaction);
    }
}
```

### 3. Fraud Detection System

```java
@Service
public class FraudDetectionService {
    
    private final List<FraudRule> fraudRules;
    private final MachineLearningModel mlModel;
    private final BlacklistService blacklistService;
    
    public FraudResult checkFraud(PaymentRequest request) {
        FraudScore score = new FraudScore();
        
        // Rule-based detection
        for (FraudRule rule : fraudRules) {
            RuleResult result = rule.evaluate(request);
            score.addScore(result.getScore(), result.getReason());
            
            if (result.isBlocking()) {
                return FraudResult.blocked(result.getReason());
            }
        }
        
        // Machine learning model
        double mlScore = mlModel.predict(extractFeatures(request));
        score.addScore(mlScore, "ML Model");
        
        // Blacklist check
        if (blacklistService.isBlacklisted(request.getCardNumber()) ||
            blacklistService.isBlacklisted(request.getIpAddress())) {
            return FraudResult.blocked("Blacklisted");
        }
        
        // Final decision
        if (score.getTotalScore() > FRAUD_THRESHOLD) {
            return FraudResult.fraudulent(score);
        } else if (score.getTotalScore() > REVIEW_THRESHOLD) {
            return FraudResult.review(score);
        } else {
            return FraudResult.clean(score);
        }
    }
    
    private Map<String, Double> extractFeatures(PaymentRequest request) {
        Map<String, Double> features = new HashMap<>();
        
        // Transaction features
        features.put("amount", request.getAmount().doubleValue());
        features.put("hour_of_day", (double) LocalTime.now().getHour());
        features.put("day_of_week", (double) LocalDate.now().getDayOfWeek().getValue());
        
        // Customer features
        features.put("customer_age_days", getCustomerAgeDays(request.getCustomerId()));
        features.put("customer_transaction_count", getCustomerTransactionCount(request.getCustomerId()));
        
        // Geographic features
        features.put("country_risk_score", getCountryRiskScore(request.getBillingCountry()));
        features.put("ip_country_mismatch", isIpCountryMismatch(request) ? 1.0 : 0.0);
        
        // Velocity features
        features.put("transactions_last_hour", getTransactionCountLastHour(request.getCustomerId()));
        features.put("transactions_last_day", getTransactionCountLastDay(request.getCustomerId()));
        
        return features;
    }
}

public abstract class FraudRule {
    protected final String name;
    protected final double weight;
    
    public FraudRule(String name, double weight) {
        this.name = name;
        this.weight = weight;
    }
    
    public abstract RuleResult evaluate(PaymentRequest request);
}

public class VelocityRule extends FraudRule {
    private final TransactionService transactionService;
    private final int maxTransactionsPerHour;
    
    public VelocityRule(TransactionService transactionService, int maxTransactionsPerHour) {
        super("Velocity Rule", 0.3);
        this.transactionService = transactionService;
        this.maxTransactionsPerHour = maxTransactionsPerHour;
    }
    
    @Override
    public RuleResult evaluate(PaymentRequest request) {
        Instant oneHourAgo = Instant.now().minus(1, ChronoUnit.HOURS);
        
        long transactionCount = transactionService.countTransactionsSince(
            request.getCustomerId(), 
            oneHourAgo
        );
        
        if (transactionCount >= maxTransactionsPerHour) {
            return RuleResult.blocking("Too many transactions in the last hour");
        } else if (transactionCount >= maxTransactionsPerHour * 0.8) {
            return RuleResult.scored(weight * 0.8, "High transaction velocity");
        }
        
        return RuleResult.clean();
    }
}
```

### 4. Security and Encryption

```java
@Service
public class EncryptionService {
    
    private final AESUtil aesUtil;
    private final RSAUtil rsaUtil;
    private final TokenizationService tokenizationService;
    
    public String encryptSensitiveData(String data) {
        // Use AES for symmetric encryption of sensitive data
        return aesUtil.encrypt(data);
    }
    
    public String decryptSensitiveData(String encryptedData) {
        return aesUtil.decrypt(encryptedData);
    }
    
    public String tokenizeCardNumber(String cardNumber) {
        // Replace card number with a token
        return tokenizationService.tokenize(cardNumber);
    }
    
    public String detokenizeCardNumber(String token) {
        return tokenizationService.detokenize(token);
    }
    
    public String hashPII(String pii) {
        // One-way hash for PII that doesn't need to be retrieved
        return DigestUtils.sha256Hex(pii + getSalt());
    }
}

@Component
public class PCIComplianceFilter implements Filter {
    
    private final Set<String> sensitiveFields = Set.of(
        "cardNumber", "cvv", "pin", "ssn"
    );
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) 
            throws IOException, ServletException {
        
        // Wrap request to mask sensitive data in logs
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        PCICompliantRequestWrapper wrappedRequest = new PCICompliantRequestWrapper(httpRequest);
        
        try {
            chain.doFilter(wrappedRequest, response);
        } finally {
            // Ensure sensitive data is not logged
            clearSensitiveDataFromMDC();
        }
    }
    
    private void clearSensitiveDataFromMDC() {
        sensitiveFields.forEach(MDC::remove);
    }
}

@Service
public class AuditService {
    
    private final AuditLogRepository auditLogRepository;
    
    public void logTransactionChange(Transaction transaction) {
        AuditLog auditLog = AuditLog.builder()
            .entityType("Transaction")
            .entityId(transaction.getTransactionId())
            .action("UPDATE")
            .oldStatus(transaction.getPreviousStatus())
            .newStatus(transaction.getStatus())
            .userId(getCurrentUserId())
            .timestamp(Instant.now())
            .ipAddress(getCurrentUserIpAddress())
            .build();
        
        auditLogRepository.save(auditLog);
    }
    
    public void logPaymentAttempt(PaymentRequest request, PaymentResult result) {
        AuditLog auditLog = AuditLog.builder()
            .entityType("Payment")
            .entityId(request.getTransactionId())
            .action("PAYMENT_ATTEMPT")
            .details(createPaymentAuditDetails(request, result))
            .userId(request.getMerchantId())
            .timestamp(Instant.now())
            .ipAddress(request.getIpAddress())
            .build();
        
        auditLogRepository.save(auditLog);
    }
}
```

### 5. Recurring Payments

```java
@Service
public class RecurringPaymentService {
    
    private final SubscriptionRepository subscriptionRepository;
    private final PaymentService paymentService;
    private final ScheduledExecutorService scheduler;
    
    @PostConstruct
    public void initializeScheduler() {
        // Schedule recurring payment processing every hour
        scheduler.scheduleAtFixedRate(this::processDuePayments, 0, 1, TimeUnit.HOURS);
    }
    
    public Subscription createSubscription(SubscriptionRequest request) {
        Subscription subscription = Subscription.builder()
            .subscriptionId(generateSubscriptionId())
            .merchantId(request.getMerchantId())
            .customerId(request.getCustomerId())
            .amount(request.getAmount())
            .currency(request.getCurrency())
            .frequency(request.getFrequency())
            .paymentMethodToken(request.getPaymentMethodToken())
            .status(SubscriptionStatus.ACTIVE)
            .nextPaymentDate(calculateNextPaymentDate(request.getFrequency()))
            .createdAt(Instant.now())
            .build();
        
        return subscriptionRepository.save(subscription);
    }
    
    public void processDuePayments() {
        Instant now = Instant.now();
        List<Subscription> dueSubscriptions = subscriptionRepository
            .findByStatusAndNextPaymentDateBefore(SubscriptionStatus.ACTIVE, now);
        
        for (Subscription subscription : dueSubscriptions) {
            try {
                processSubscriptionPayment(subscription);
            } catch (Exception e) {
                handleSubscriptionPaymentFailure(subscription, e);
            }
        }
    }
    
    private void processSubscriptionPayment(Subscription subscription) {
        PaymentRequest paymentRequest = PaymentRequest.builder()
            .merchantId(subscription.getMerchantId())
            .customerId(subscription.getCustomerId())
            .amount(subscription.getAmount())
            .currency(subscription.getCurrency())
            .paymentMethodToken(subscription.getPaymentMethodToken())
            .description("Subscription payment for " + subscription.getSubscriptionId())
            .build();
        
        PaymentResult result = paymentService.processPayment(paymentRequest);
        
        if (result.isSuccess()) {
            // Update subscription for next payment
            subscription.setNextPaymentDate(
                calculateNextPaymentDate(subscription.getFrequency())
            );
            subscription.setLastPaymentDate(Instant.now());
            subscription.setFailedAttempts(0);
            subscriptionRepository.save(subscription);
            
        } else {
            handleFailedSubscriptionPayment(subscription, result);
        }
    }
}
```

## 📊 Monitoring and Compliance

### Key Metrics
- **Transaction success rate**: Percentage of successful transactions
- **Average processing time**: Time from request to response
- **Fraud detection accuracy**: False positive/negative rates
- **System availability**: Uptime percentage
- **PCI compliance score**: Security compliance metrics

### Alerting
- Transaction failure rate > 5%
- Processing time > 1 second
- Fraud detection system down
- Database connection issues
- Security breach attempts

This payment system design provides a secure, scalable foundation for processing financial transactions while maintaining PCI compliance and fraud protection.
