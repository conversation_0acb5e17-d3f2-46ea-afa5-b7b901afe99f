# Ride Sharing System Design

Design a ride-sharing platform like Uber, Lyft, or Didi that connects riders with drivers, handles real-time location tracking, dynamic pricing, and payment processing.

## 📋 Requirements

### Functional Requirements
1. **User Management**: Riders and drivers can register and manage profiles
2. **Location Services**: Real-time location tracking and updates
3. **Ride Matching**: Match riders with nearby available drivers
4. **Trip Management**: Handle trip lifecycle from request to completion
5. **Dynamic Pricing**: Surge pricing based on supply and demand
6. **Payment Processing**: Handle payments, tips, and driver payouts
7. **Navigation**: Provide turn-by-turn navigation and ETA
8. **Notifications**: Real-time updates on ride status

### Non-Functional Requirements
1. **Scale**: 100M users, 10M daily rides
2. **Availability**: 99.99% uptime
3. **Latency**: < 1 second for location updates, < 5 seconds for matching
4. **Consistency**: Strong consistency for payments, eventual for locations
5. **Global**: Support multiple cities and countries

## 🔢 Capacity Estimation

### User & Trip Estimates
- **Total users**: 100M (80M riders, 20M drivers)
- **Daily active users**: 10M riders, 2M drivers
- **Daily trips**: 10M trips
- **Peak trips**: 50K trips/hour
- **Average trip duration**: 30 minutes

### Location Updates
- **Active drivers**: 500K concurrent
- **Location updates**: Every 5 seconds
- **Location update rate**: 100K updates/second
- **Daily location data**: 500K × 17,280 updates = 8.6B updates/day

### Storage Estimates
- **User profiles**: 100M × 1KB = 100GB
- **Trip data**: 10M trips/day × 2KB = 20GB/day
- **Location data**: 8.6B updates × 50 bytes = 430GB/day
- **Annual storage**: ~160TB/year

## 🏗️ High-Level Architecture

```mermaid
graph TB
    subgraph "Client Applications"
        RiderApp[Rider Mobile App]
        DriverApp[Driver Mobile App]
        WebApp[Web Application]
    end

    subgraph "API Gateway & Load Balancing"
        APIGateway[API Gateway]
        LoadBalancer[Load Balancer]
    end

    subgraph "Core Services"
        UserService[User Service]
        LocationService[Location Service]
        MatchingService[Matching Service]
        TripService[Trip Service]
        PaymentService[Payment Service]
        PricingService[Pricing Service]
        NotificationService[Notification Service]
    end

    subgraph "Real-time Systems"
        LocationTracker[Location Tracker]
        WebSocketGateway[WebSocket Gateway]
        EventStream[Event Stream - Kafka]
    end

    subgraph "Geospatial & Matching"
        GeoIndex[Geospatial Index - Redis]
        MatchingEngine[Matching Engine]
        ETAService[ETA Service]
        NavigationService[Navigation Service]
    end

    subgraph "Data Storage"
        UserDB[(User Database)]
        TripDB[(Trip Database)]
        LocationDB[(Location Database)]
        PaymentDB[(Payment Database)]
        Cache[Redis Cache]
    end

    subgraph "External Services"
        MapService[Maps API - Google/Mapbox]
        PaymentGateway[Payment Gateway]
        SMSService[SMS Service]
        PushService[Push Notification Service]
    end

    RiderApp --> APIGateway
    DriverApp --> APIGateway
    WebApp --> APIGateway

    APIGateway --> LoadBalancer
    LoadBalancer --> UserService
    LoadBalancer --> LocationService
    LoadBalancer --> MatchingService
    LoadBalancer --> TripService
    LoadBalancer --> PaymentService
    LoadBalancer --> PricingService

    LocationService --> LocationTracker
    LocationTracker --> GeoIndex
    LocationTracker --> EventStream

    MatchingService --> MatchingEngine
    MatchingEngine --> GeoIndex
    MatchingEngine --> ETAService

    TripService --> NavigationService
    NavigationService --> MapService

    PaymentService --> PaymentGateway
    NotificationService --> SMSService
    NotificationService --> PushService

    UserService --> UserDB
    TripService --> TripDB
    LocationService --> LocationDB
    PaymentService --> PaymentDB

    EventStream --> WebSocketGateway
    WebSocketGateway --> RiderApp
    WebSocketGateway --> DriverApp
```

## 🔧 Detailed Design

### 1. Location Tracking System

```java
@Service
public class LocationService {

    private final GeoSpatialIndex geoIndex;
    private final LocationRepository locationRepository;
    private final EventPublisher eventPublisher;

    @PostMapping("/location/update")
    public ResponseEntity<Void> updateLocation(@RequestBody LocationUpdate update) {
        // Validate location data
        if (!isValidLocation(update.getLatitude(), update.getLongitude())) {
            return ResponseEntity.badRequest().build();
        }

        // Update driver location in geospatial index
        if (update.getUserType() == UserType.DRIVER) {
            geoIndex.updateDriverLocation(
                update.getDriverId(),
                update.getLatitude(),
                update.getLongitude(),
                update.getHeading(),
                update.getTimestamp()
            );
        }

        // Store location history
        LocationRecord record = LocationRecord.builder()
            .userId(update.getUserId())
            .latitude(update.getLatitude())
            .longitude(update.getLongitude())
            .timestamp(update.getTimestamp())
            .accuracy(update.getAccuracy())
            .build();

        locationRepository.save(record);

        // Publish location update event
        eventPublisher.publish(new LocationUpdatedEvent(update));

        return ResponseEntity.ok().build();
    }

    public List<NearbyDriver> findNearbyDrivers(double latitude, double longitude,
                                                double radiusKm, int limit) {
        return geoIndex.findNearbyDrivers(latitude, longitude, radiusKm, limit);
    }
}

@Component
public class GeoSpatialIndex {

    private final RedisGeoCommands geoCommands;
    private static final String DRIVER_LOCATION_KEY = "driver_locations";

    public void updateDriverLocation(String driverId, double lat, double lon,
                                   double heading, Instant timestamp) {
        // Update location in Redis geospatial index
        Point point = new Point(lon, lat);
        geoCommands.geoAdd(DRIVER_LOCATION_KEY, point, driverId);

        // Store additional metadata
        DriverLocationMetadata metadata = DriverLocationMetadata.builder()
            .driverId(driverId)
            .heading(heading)
            .timestamp(timestamp)
            .status(getDriverStatus(driverId))
            .build();

        redisTemplate.opsForHash().put("driver_metadata", driverId, metadata);
    }

    public List<NearbyDriver> findNearbyDrivers(double lat, double lon,
                                               double radiusKm, int limit) {
        Circle circle = new Circle(new Point(lon, lat), new Distance(radiusKm, Metrics.KILOMETERS));

        GeoResults<RedisGeoCommands.GeoLocation<String>> results =
            geoCommands.geoRadius(DRIVER_LOCATION_KEY, circle,
                GeoRadiusCommandArgs.newGeoRadiusArgs()
                    .limit(limit)
                    .sortAscending()
                    .includeDistance()
                    .includeCoordinates());

        return results.getContent().stream()
            .map(this::convertToNearbyDriver)
            .filter(driver -> driver.getStatus() == DriverStatus.AVAILABLE)
            .collect(Collectors.toList());
    }
}
```

### 2. Ride Matching Algorithm

```java
@Service
public class MatchingService {

    private final LocationService locationService;
    private final ETAService etaService;
    private final PricingService pricingService;
    private final DriverService driverService;

    public MatchingResult findMatch(RideRequest request) {
        // Find nearby available drivers
        List<NearbyDriver> nearbyDrivers = locationService.findNearbyDrivers(
            request.getPickupLatitude(),
            request.getPickupLongitude(),
            SEARCH_RADIUS_KM,
            MAX_DRIVERS_TO_CONSIDER
        );

        if (nearbyDrivers.isEmpty()) {
            return MatchingResult.noDriversAvailable();
        }

        // Score and rank drivers
        List<DriverMatch> rankedDrivers = scoreDrivers(request, nearbyDrivers);

        // Try to match with best drivers in order
        for (DriverMatch driverMatch : rankedDrivers) {
            if (tryMatchWithDriver(request, driverMatch.getDriver())) {
                return MatchingResult.success(driverMatch.getDriver());
            }
        }

        return MatchingResult.noMatchFound();
    }

    private List<DriverMatch> scoreDrivers(RideRequest request, List<NearbyDriver> drivers) {
        return drivers.stream()
            .map(driver -> {
                double score = calculateDriverScore(request, driver);
                return new DriverMatch(driver, score);
            })
            .sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
            .collect(Collectors.toList());
    }

    private double calculateDriverScore(RideRequest request, NearbyDriver driver) {
        double score = 0.0;

        // Distance factor (closer is better)
        double distanceScore = 1.0 / (1.0 + driver.getDistanceKm());
        score += distanceScore * 0.4;

        // Driver rating factor
        double ratingScore = driver.getRating() / 5.0;
        score += ratingScore * 0.3;

        // ETA factor
        int etaMinutes = etaService.calculateETA(
            driver.getLatitude(), driver.getLongitude(),
            request.getPickupLatitude(), request.getPickupLongitude()
        );
        double etaScore = 1.0 / (1.0 + etaMinutes / 10.0);
        score += etaScore * 0.2;

        // Vehicle type match
        if (driver.getVehicleType() == request.getPreferredVehicleType()) {
            score += 0.1;
        }

        return score;
    }

    private boolean tryMatchWithDriver(RideRequest request, NearbyDriver driver) {
        // Check if driver is still available
        if (!driverService.isAvailable(driver.getDriverId())) {
            return false;
        }

        // Try to reserve driver (atomic operation)
        boolean reserved = driverService.reserveDriver(driver.getDriverId(), request.getRideId());

        if (reserved) {
            // Send ride request to driver
            sendRideRequestToDriver(request, driver);
            return true;
        }

        return false;
    }
}
```

### 3. Dynamic Pricing System

```java
@Service
public class PricingService {

    private final DemandAnalyzer demandAnalyzer;
    private final SupplyAnalyzer supplyAnalyzer;
    private final PricingConfigRepository pricingConfigRepository;

    public PriceEstimate calculatePrice(PriceRequest request) {
        // Get base pricing configuration
        PricingConfig config = pricingConfigRepository.findByCity(request.getCityId());

        // Calculate base price
        double distance = calculateDistance(request.getPickupLocation(), request.getDropoffLocation());
        double baseFare = config.getBaseFare();
        double distanceFare = distance * config.getPricePerKm();
        double timeFare = request.getEstimatedDurationMinutes() * config.getPricePerMinute();

        double basePrice = baseFare + distanceFare + timeFare;

        // Apply surge multiplier
        double surgeMultiplier = calculateSurgeMultiplier(request);
        double finalPrice = basePrice * surgeMultiplier;

        return PriceEstimate.builder()
            .basePrice(basePrice)
            .surgeMultiplier(surgeMultiplier)
            .finalPrice(finalPrice)
            .currency(config.getCurrency())
            .breakdown(createPriceBreakdown(baseFare, distanceFare, timeFare, surgeMultiplier))
            .build();
    }

    private double calculateSurgeMultiplier(PriceRequest request) {
        // Analyze demand in the area
        DemandMetrics demand = demandAnalyzer.analyzeDemand(
            request.getPickupLocation(),
            ANALYSIS_RADIUS_KM,
            Duration.ofMinutes(15)
        );

        // Analyze supply in the area
        SupplyMetrics supply = supplyAnalyzer.analyzeSupply(
            request.getPickupLocation(),
            ANALYSIS_RADIUS_KM
        );

        // Calculate demand-supply ratio
        double demandSupplyRatio = (double) demand.getRideRequests() / Math.max(1, supply.getAvailableDrivers());

        // Apply surge pricing algorithm
        if (demandSupplyRatio < 1.2) {
            return 1.0; // No surge
        } else if (demandSupplyRatio < 2.0) {
            return 1.2; // Low surge
        } else if (demandSupplyRatio < 3.0) {
            return 1.5; // Medium surge
        } else if (demandSupplyRatio < 5.0) {
            return 2.0; // High surge
        } else {
            return 2.5; // Maximum surge
        }
    }
}

@Service
public class DemandAnalyzer {

    private final RideRequestRepository rideRequestRepository;
    private final RedisTemplate<String, Object> redisTemplate;

    public DemandMetrics analyzeDemand(Location location, double radiusKm, Duration timeWindow) {
        String cacheKey = String.format("demand:%f:%f:%f:%d",
            location.getLatitude(), location.getLongitude(), radiusKm, timeWindow.toMinutes());

        // Try cache first
        DemandMetrics cached = (DemandMetrics) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }

        // Calculate demand metrics
        Instant since = Instant.now().minus(timeWindow);

        List<RideRequest> recentRequests = rideRequestRepository
            .findRecentRequestsInArea(location, radiusKm, since);

        int completedRides = (int) recentRequests.stream()
            .filter(r -> r.getStatus() == RideStatus.COMPLETED)
            .count();

        int cancelledRides = (int) recentRequests.stream()
            .filter(r -> r.getStatus() == RideStatus.CANCELLED)
            .count();

        double cancellationRate = (double) cancelledRides / Math.max(1, recentRequests.size());

        DemandMetrics metrics = DemandMetrics.builder()
            .rideRequests(recentRequests.size())
            .completedRides(completedRides)
            .cancellationRate(cancellationRate)
            .averageWaitTime(calculateAverageWaitTime(recentRequests))
            .build();

        // Cache for 2 minutes
        redisTemplate.opsForValue().set(cacheKey, metrics, Duration.ofMinutes(2));

        return metrics;
    }
}
```

### 4. Trip Management

```java
@Service
public class TripService {

    private final TripRepository tripRepository;
    private final PaymentService paymentService;
    private final NotificationService notificationService;
    private final NavigationService navigationService;

    @Transactional
    public Trip createTrip(CreateTripRequest request) {
        Trip trip = Trip.builder()
            .tripId(generateTripId())
            .riderId(request.getRiderId())
            .driverId(request.getDriverId())
            .pickupLocation(request.getPickupLocation())
            .dropoffLocation(request.getDropoffLocation())
            .status(TripStatus.CONFIRMED)
            .estimatedFare(request.getEstimatedFare())
            .createdAt(Instant.now())
            .build();

        trip = tripRepository.save(trip);

        // Send notifications
        notificationService.notifyRiderTripConfirmed(trip);
        notificationService.notifyDriverTripAssigned(trip);

        // Generate navigation route
        Route route = navigationService.calculateRoute(
            request.getPickupLocation(),
            request.getDropoffLocation()
        );
        trip.setRoute(route);

        return tripRepository.save(trip);
    }

    @Transactional
    public Trip startTrip(String tripId, String driverId) {
        Trip trip = tripRepository.findById(tripId)
            .orElseThrow(() -> new TripNotFoundException(tripId));

        // Validate driver
        if (!trip.getDriverId().equals(driverId)) {
            throw new UnauthorizedDriverException();
        }

        if (trip.getStatus() != TripStatus.CONFIRMED) {
            throw new InvalidTripStatusException("Trip cannot be started");
        }

        // Update trip status
        trip.setStatus(TripStatus.IN_PROGRESS);
        trip.setStartedAt(Instant.now());

        trip = tripRepository.save(trip);

        // Notify rider
        notificationService.notifyRiderTripStarted(trip);

        return trip;
    }

    @Transactional
    public Trip completeTrip(String tripId, String driverId, CompleteTripRequest request) {
        Trip trip = tripRepository.findById(tripId)
            .orElseThrow(() -> new TripNotFoundException(tripId));

        // Validate and update trip
        trip.setStatus(TripStatus.COMPLETED);
        trip.setCompletedAt(Instant.now());
        trip.setActualDistance(request.getActualDistance());
        trip.setActualDuration(request.getActualDuration());

        // Calculate final fare
        double finalFare = calculateFinalFare(trip, request);
        trip.setFinalFare(finalFare);

        trip = tripRepository.save(trip);

        // Process payment
        PaymentResult paymentResult = paymentService.processPayment(
            trip.getRiderId(),
            finalFare,
            trip.getTripId()
        );

        if (paymentResult.isSuccess()) {
            trip.setPaymentStatus(PaymentStatus.COMPLETED);

            // Pay driver
            paymentService.payDriver(trip.getDriverId(), calculateDriverPayout(finalFare));
        } else {
            trip.setPaymentStatus(PaymentStatus.FAILED);
        }

        // Send notifications
        notificationService.notifyTripCompleted(trip);

        return tripRepository.save(trip);
    }
}
```

This ride-sharing system design provides a comprehensive, scalable platform capable of handling millions of rides with real-time location tracking, intelligent matching, and dynamic pricing.
