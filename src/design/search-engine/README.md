# Web Search Engine System Design

Design a web search engine like Google, Bing, or DuckDuckGo that can crawl, index, and search billions of web pages with sub-second response times.

## 📋 Requirements

### Functional Requirements
1. **Web Crawling**: Discover and crawl billions of web pages
2. **Indexing**: Build searchable index of crawled content
3. **Search**: Return relevant results for user queries
4. **Ranking**: Rank results by relevance and authority
5. **Real-time Updates**: Handle new/updated content
6. **Auto-complete**: Suggest queries as users type

### Non-Functional Requirements
1. **Scale**: Index 50B web pages, handle 100K queries/second
2. **Latency**: < 200ms search response time
3. **Availability**: 99.99% uptime
4. **Storage**: Petabytes of indexed data
5. **Freshness**: Index new content within hours

## 🔢 Capacity Estimation

### Web Scale Estimates
- **Total web pages**: 50B pages
- **Average page size**: 100KB
- **Total content**: 50B × 100KB = 5PB
- **Index size**: ~20% of content = 1PB

### Traffic Estimates
- **Search queries**: 100K queries/second
- **Peak traffic**: 3x average = 300K queries/second
- **Crawl rate**: 10M pages/day = ~115 pages/second

### Storage Estimates
- **Forward index**: 1PB (page content + metadata)
- **Inverted index**: 500TB (terms → documents)
- **Link graph**: 100TB (page relationships)
- **Total storage**: ~1.6PB

## 🏗️ High-Level Architecture

```mermaid
graph TB
    subgraph "User Interface"
        WebUI[Web Interface]
        MobileApp[Mobile Apps]
        API[Search API]
    end
    
    subgraph "Search Frontend"
        QueryProcessor[Query Processor]
        AutoComplete[Auto-complete Service]
        ResultRenderer[Result Renderer]
    end
    
    subgraph "Search Backend"
        SearchCoordinator[Search Coordinator]
        IndexShards[Index Shards]
        RankingService[Ranking Service]
        PersonalizationService[Personalization Service]
    end
    
    subgraph "Crawling System"
        CrawlCoordinator[Crawl Coordinator]
        CrawlerFleet[Crawler Fleet]
        URLQueue[URL Queue]
        DuplicateDetector[Duplicate Detector]
    end
    
    subgraph "Indexing Pipeline"
        ContentProcessor[Content Processor]
        TextExtractor[Text Extractor]
        IndexBuilder[Index Builder]
        LinkAnalyzer[Link Analyzer]
    end
    
    subgraph "Storage Layer"
        ForwardIndex[(Forward Index)]
        InvertedIndex[(Inverted Index)]
        LinkGraph[(Link Graph)]
        PageMetadata[(Page Metadata)]
        UserData[(User Data)]
    end
    
    subgraph "Infrastructure"
        LoadBalancer[Load Balancer]
        CDN[Content Delivery Network]
        MessageQueue[Message Queue]
        Cache[Distributed Cache]
    end
    
    WebUI --> LoadBalancer
    MobileApp --> LoadBalancer
    API --> LoadBalancer
    
    LoadBalancer --> QueryProcessor
    QueryProcessor --> SearchCoordinator
    SearchCoordinator --> IndexShards
    SearchCoordinator --> RankingService
    
    CrawlCoordinator --> CrawlerFleet
    CrawlerFleet --> URLQueue
    CrawlerFleet --> ContentProcessor
    
    ContentProcessor --> IndexBuilder
    IndexBuilder --> InvertedIndex
    IndexBuilder --> ForwardIndex
    
    LinkAnalyzer --> LinkGraph
    RankingService --> LinkGraph
    
    SearchCoordinator --> Cache
    QueryProcessor --> AutoComplete
```

## 🔧 Detailed Design

### 1. Web Crawler Architecture

```java
public class WebCrawler {
    private final URLQueue urlQueue;
    private final HttpClient httpClient;
    private final RobotsTxtChecker robotsChecker;
    private final DuplicateDetector duplicateDetector;
    private final ContentProcessor contentProcessor;
    
    public void crawl() {
        while (true) {
            try {
                CrawlTask task = urlQueue.poll();
                if (task == null) {
                    Thread.sleep(1000);
                    continue;
                }
                
                if (!shouldCrawl(task.getUrl())) {
                    continue;
                }
                
                CrawlResult result = crawlPage(task.getUrl());
                if (result.isSuccess()) {
                    processContent(result);
                    extractLinks(result);
                }
                
            } catch (Exception e) {
                handleCrawlError(e);
            }
        }
    }
    
    private boolean shouldCrawl(String url) {
        // Check robots.txt
        if (!robotsChecker.isAllowed(url)) {
            return false;
        }
        
        // Check if already crawled recently
        if (duplicateDetector.isRecentlyCrawled(url)) {
            return false;
        }
        
        // Check URL quality/spam filters
        return urlQualityFilter.isHighQuality(url);
    }
    
    private CrawlResult crawlPage(String url) {
        try {
            HttpResponse response = httpClient.get(url);
            
            return CrawlResult.builder()
                .url(url)
                .statusCode(response.getStatusCode())
                .content(response.getBody())
                .headers(response.getHeaders())
                .crawlTime(Instant.now())
                .success(response.getStatusCode() == 200)
                .build();
                
        } catch (Exception e) {
            return CrawlResult.failed(url, e);
        }
    }
    
    private void processContent(CrawlResult result) {
        // Send to content processing pipeline
        contentProcessor.process(result);
    }
    
    private void extractLinks(CrawlResult result) {
        List<String> links = linkExtractor.extractLinks(result.getContent(), result.getUrl());
        
        for (String link : links) {
            if (isValidLink(link)) {
                urlQueue.add(new CrawlTask(link, calculatePriority(link)));
            }
        }
    }
}
```

### 2. Content Processing Pipeline

```java
public class ContentProcessor {
    private final TextExtractor textExtractor;
    private final LanguageDetector languageDetector;
    private final ContentClassifier contentClassifier;
    private final IndexBuilder indexBuilder;
    
    public void process(CrawlResult crawlResult) {
        try {
            // Extract text content
            TextContent textContent = textExtractor.extract(crawlResult.getContent());
            
            // Detect language
            String language = languageDetector.detect(textContent.getText());
            
            // Classify content type
            ContentType contentType = contentClassifier.classify(textContent);
            
            // Create document
            Document document = Document.builder()
                .url(crawlResult.getUrl())
                .title(textContent.getTitle())
                .content(textContent.getText())
                .language(language)
                .contentType(contentType)
                .crawlTime(crawlResult.getCrawlTime())
                .build();
            
            // Send to indexing
            indexBuilder.addDocument(document);
            
        } catch (Exception e) {
            handleProcessingError(crawlResult.getUrl(), e);
        }
    }
}

public class TextExtractor {
    public TextContent extract(String htmlContent) {
        org.jsoup.nodes.Document doc = Jsoup.parse(htmlContent);
        
        // Extract title
        String title = doc.title();
        
        // Extract main content (remove navigation, ads, etc.)
        String mainContent = extractMainContent(doc);
        
        // Extract metadata
        Map<String, String> metadata = extractMetadata(doc);
        
        return TextContent.builder()
            .title(title)
            .text(mainContent)
            .metadata(metadata)
            .build();
    }
    
    private String extractMainContent(org.jsoup.nodes.Document doc) {
        // Remove script and style elements
        doc.select("script, style").remove();
        
        // Focus on main content areas
        Elements mainContent = doc.select("main, article, .content, #content");
        if (!mainContent.isEmpty()) {
            return mainContent.text();
        }
        
        // Fallback to body content
        return doc.body().text();
    }
}
```

### 3. Inverted Index Structure

```java
public class InvertedIndex {
    private final Map<String, PostingList> termIndex;
    private final TokenAnalyzer tokenAnalyzer;
    
    public InvertedIndex() {
        this.termIndex = new ConcurrentHashMap<>();
        this.tokenAnalyzer = new TokenAnalyzer();
    }
    
    public void addDocument(Document document) {
        String documentId = document.getUrl();
        List<String> tokens = tokenAnalyzer.analyze(document.getContent());
        
        // Calculate term frequencies
        Map<String, Integer> termFrequencies = calculateTermFrequencies(tokens);
        
        for (Map.Entry<String, Integer> entry : termFrequencies.entrySet()) {
            String term = entry.getKey();
            int frequency = entry.getValue();
            
            PostingList postingList = termIndex.computeIfAbsent(term, k -> new PostingList());
            postingList.addPosting(new Posting(documentId, frequency));
        }
    }
    
    public PostingList getPostingList(String term) {
        return termIndex.get(term.toLowerCase());
    }
    
    public List<String> search(String query) {
        List<String> queryTerms = tokenAnalyzer.analyze(query);
        
        if (queryTerms.isEmpty()) {
            return Collections.emptyList();
        }
        
        // Get posting lists for all query terms
        List<PostingList> postingLists = queryTerms.stream()
            .map(this::getPostingList)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        
        if (postingLists.isEmpty()) {
            return Collections.emptyList();
        }
        
        // Intersect posting lists to find documents containing all terms
        return intersectPostingLists(postingLists);
    }
    
    private List<String> intersectPostingLists(List<PostingList> postingLists) {
        if (postingLists.size() == 1) {
            return postingLists.get(0).getDocumentIds();
        }
        
        // Sort by size (smallest first for efficiency)
        postingLists.sort(Comparator.comparing(PostingList::size));
        
        Set<String> result = new HashSet<>(postingLists.get(0).getDocumentIds());
        
        for (int i = 1; i < postingLists.size(); i++) {
            result.retainAll(postingLists.get(i).getDocumentIds());
        }
        
        return new ArrayList<>(result);
    }
}

public class PostingList {
    private final List<Posting> postings;
    
    public PostingList() {
        this.postings = new ArrayList<>();
    }
    
    public void addPosting(Posting posting) {
        postings.add(posting);
    }
    
    public List<String> getDocumentIds() {
        return postings.stream()
            .map(Posting::getDocumentId)
            .collect(Collectors.toList());
    }
    
    public int size() {
        return postings.size();
    }
}

public class Posting {
    private final String documentId;
    private final int termFrequency;
    private final List<Integer> positions; // For phrase queries
    
    public Posting(String documentId, int termFrequency) {
        this.documentId = documentId;
        this.termFrequency = termFrequency;
        this.positions = new ArrayList<>();
    }
    
    // Getters
    public String getDocumentId() { return documentId; }
    public int getTermFrequency() { return termFrequency; }
    public List<Integer> getPositions() { return positions; }
}
```

### 4. Ranking Algorithm (Simplified PageRank + TF-IDF)

```java
public class RankingService {
    private final PageRankCalculator pageRankCalculator;
    private final TfIdfCalculator tfIdfCalculator;
    private final LinkGraph linkGraph;
    
    public List<SearchResult> rankResults(String query, List<String> documentIds) {
        List<SearchResult> results = new ArrayList<>();
        
        for (String documentId : documentIds) {
            double relevanceScore = calculateRelevanceScore(query, documentId);
            double authorityScore = pageRankCalculator.getPageRank(documentId);
            double freshnesScore = calculateFreshnessScore(documentId);
            
            // Combined scoring
            double finalScore = 0.6 * relevanceScore + 0.3 * authorityScore + 0.1 * freshnesScore;
            
            results.add(new SearchResult(documentId, finalScore));
        }
        
        // Sort by score (descending)
        results.sort((a, b) -> Double.compare(b.getScore(), a.getScore()));
        
        return results;
    }
    
    private double calculateRelevanceScore(String query, String documentId) {
        List<String> queryTerms = tokenAnalyzer.analyze(query);
        double score = 0.0;
        
        for (String term : queryTerms) {
            double tfIdf = tfIdfCalculator.calculate(term, documentId);
            score += tfIdf;
        }
        
        return score / queryTerms.size(); // Normalize by query length
    }
    
    private double calculateFreshnessScore(String documentId) {
        Instant crawlTime = getCrawlTime(documentId);
        long daysSinceCrawl = ChronoUnit.DAYS.between(crawlTime, Instant.now());
        
        // Exponential decay: newer content gets higher score
        return Math.exp(-daysSinceCrawl / 30.0); // 30-day half-life
    }
}

public class PageRankCalculator {
    private final LinkGraph linkGraph;
    private final Map<String, Double> pageRankScores;
    private static final double DAMPING_FACTOR = 0.85;
    private static final int MAX_ITERATIONS = 100;
    private static final double CONVERGENCE_THRESHOLD = 1e-6;
    
    public void calculatePageRank() {
        Set<String> allPages = linkGraph.getAllPages();
        int numPages = allPages.size();
        
        // Initialize PageRank scores
        double initialScore = 1.0 / numPages;
        for (String page : allPages) {
            pageRankScores.put(page, initialScore);
        }
        
        // Iterative calculation
        for (int iteration = 0; iteration < MAX_ITERATIONS; iteration++) {
            Map<String, Double> newScores = new HashMap<>();
            double maxDiff = 0.0;
            
            for (String page : allPages) {
                double newScore = calculatePageScore(page, numPages);
                newScores.put(page, newScore);
                
                double diff = Math.abs(newScore - pageRankScores.get(page));
                maxDiff = Math.max(maxDiff, diff);
            }
            
            pageRankScores.putAll(newScores);
            
            if (maxDiff < CONVERGENCE_THRESHOLD) {
                break; // Converged
            }
        }
    }
    
    private double calculatePageScore(String page, int totalPages) {
        Set<String> incomingLinks = linkGraph.getIncomingLinks(page);
        double score = (1.0 - DAMPING_FACTOR) / totalPages;
        
        for (String linkingPage : incomingLinks) {
            int outgoingLinksCount = linkGraph.getOutgoingLinksCount(linkingPage);
            if (outgoingLinksCount > 0) {
                score += DAMPING_FACTOR * pageRankScores.get(linkingPage) / outgoingLinksCount;
            }
        }
        
        return score;
    }
    
    public double getPageRank(String page) {
        return pageRankScores.getOrDefault(page, 0.0);
    }
}
```

### 5. Query Processing

```java
public class QueryProcessor {
    private final QueryAnalyzer queryAnalyzer;
    private final SpellChecker spellChecker;
    private final QueryExpander queryExpander;
    
    public ProcessedQuery processQuery(String rawQuery) {
        // Clean and normalize query
        String cleanQuery = cleanQuery(rawQuery);
        
        // Spell correction
        String correctedQuery = spellChecker.correct(cleanQuery);
        
        // Query analysis
        QueryAnalysis analysis = queryAnalyzer.analyze(correctedQuery);
        
        // Query expansion (synonyms, related terms)
        List<String> expandedTerms = queryExpander.expand(analysis.getTerms());
        
        return ProcessedQuery.builder()
            .originalQuery(rawQuery)
            .correctedQuery(correctedQuery)
            .terms(analysis.getTerms())
            .expandedTerms(expandedTerms)
            .queryType(analysis.getQueryType())
            .build();
    }
    
    private String cleanQuery(String query) {
        return query.trim()
            .toLowerCase()
            .replaceAll("[^a-zA-Z0-9\\s]", " ")
            .replaceAll("\\s+", " ");
    }
}

public class AutoCompleteService {
    private final TrieNode root;
    private final QueryFrequencyTracker frequencyTracker;
    
    public AutoCompleteService() {
        this.root = new TrieNode();
        this.frequencyTracker = new QueryFrequencyTracker();
    }
    
    public List<String> getSuggestions(String prefix, int maxSuggestions) {
        TrieNode node = findNode(prefix);
        if (node == null) {
            return Collections.emptyList();
        }
        
        List<String> suggestions = new ArrayList<>();
        collectSuggestions(node, prefix, suggestions, maxSuggestions);
        
        // Sort by frequency
        suggestions.sort((a, b) -> Integer.compare(
            frequencyTracker.getFrequency(b),
            frequencyTracker.getFrequency(a)
        ));
        
        return suggestions.subList(0, Math.min(maxSuggestions, suggestions.size()));
    }
    
    public void addQuery(String query) {
        TrieNode current = root;
        
        for (char c : query.toCharArray()) {
            current.children.computeIfAbsent(c, k -> new TrieNode());
            current = current.children.get(c);
        }
        
        current.isEndOfWord = true;
        frequencyTracker.incrementFrequency(query);
    }
}
```

## ⚡ Scalability Solutions

### 1. Index Sharding Strategy

```java
public class IndexShardManager {
    private final List<IndexShard> shards;
    private final ConsistentHashRing hashRing;
    
    public IndexShardManager(int numShards) {
        this.shards = new ArrayList<>();
        this.hashRing = new ConsistentHashRing();
        
        for (int i = 0; i < numShards; i++) {
            IndexShard shard = new IndexShard("shard-" + i);
            shards.add(shard);
            hashRing.addNode(shard);
        }
    }
    
    public void addDocument(Document document) {
        IndexShard shard = hashRing.getNode(document.getUrl());
        shard.addDocument(document);
    }
    
    public List<SearchResult> search(String query) {
        List<CompletableFuture<List<SearchResult>>> futures = shards.stream()
            .map(shard -> CompletableFuture.supplyAsync(() -> shard.search(query)))
            .collect(Collectors.toList());
        
        // Merge results from all shards
        return futures.stream()
            .map(CompletableFuture::join)
            .flatMap(List::stream)
            .sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
            .limit(1000) // Top 1000 results
            .collect(Collectors.toList());
    }
}
```

### 2. Distributed Crawling

```java
public class DistributedCrawlCoordinator {
    private final List<CrawlerNode> crawlerNodes;
    private final URLQueue globalUrlQueue;
    private final BloomFilter crawledUrlsFilter;
    
    public void distributeCrawlTasks() {
        while (true) {
            List<CrawlTask> tasks = globalUrlQueue.poll(1000); // Batch of 1000 URLs
            
            if (tasks.isEmpty()) {
                Thread.sleep(5000);
                continue;
            }
            
            // Distribute tasks among crawler nodes
            Map<CrawlerNode, List<CrawlTask>> distribution = distributeTasks(tasks);
            
            for (Map.Entry<CrawlerNode, List<CrawlTask>> entry : distribution.entrySet()) {
                entry.getKey().assignTasks(entry.getValue());
            }
        }
    }
    
    private Map<CrawlerNode, List<CrawlTask>> distributeTasks(List<CrawlTask> tasks) {
        Map<CrawlerNode, List<CrawlTask>> distribution = new HashMap<>();
        
        for (CrawlTask task : tasks) {
            // Assign based on domain to respect robots.txt and rate limits
            String domain = extractDomain(task.getUrl());
            CrawlerNode node = selectNodeForDomain(domain);
            
            distribution.computeIfAbsent(node, k -> new ArrayList<>()).add(task);
        }
        
        return distribution;
    }
}
```

This search engine design provides a scalable foundation capable of indexing billions of web pages and serving millions of search queries with sub-second response times.
