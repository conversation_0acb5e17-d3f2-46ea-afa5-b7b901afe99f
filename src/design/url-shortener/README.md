# URL Shortener System Design

Design a URL shortening service like bit.ly, tinyurl.com, or goo.gl that converts long URLs into short, manageable links.

## 📋 Requirements

### Functional Requirements
1. **URL Shortening**: Convert long URLs to short URLs
2. **URL Redirection**: Redirect short URLs to original long URLs
3. **Custom Aliases**: Allow users to create custom short URLs (optional)
4. **Expiration**: URLs can have expiration dates
5. **Analytics**: Track click counts and basic analytics

### Non-Functional Requirements
1. **Scale**: 100M URLs shortened per day, 10:1 read/write ratio
2. **Availability**: 99.9% uptime
3. **Latency**: < 100ms for redirection
4. **Storage**: URLs stored for 5 years
5. **Security**: Prevent abuse and spam

## 🔢 Capacity Estimation

### Traffic Estimates
- **Write requests**: 100M URLs/day = ~1,160 URLs/second
- **Read requests**: 1B redirections/day = ~11,600 requests/second
- **Peak traffic**: 5x average = ~58,000 requests/second

### Storage Estimates
- **URLs per year**: 100M × 365 = 36.5B URLs
- **Storage per URL**: ~500 bytes (URL + metadata)
- **Total storage (5 years)**: 36.5B × 5 × 500 bytes = ~91TB

### Bandwidth Estimates
- **Incoming data**: 1,160 × 500 bytes = ~580 KB/second
- **Outgoing data**: 11,600 × 500 bytes = ~5.8 MB/second

## 🏗️ High-Level Architecture

```mermaid
graph TB
    Client[Client Applications] --> LB[Load Balancer]
    LB --> API1[API Server 1]
    LB --> API2[API Server 2]
    LB --> API3[API Server 3]
    
    API1 --> Cache[Redis Cache]
    API2 --> Cache
    API3 --> Cache
    
    API1 --> DB[(Primary Database)]
    API2 --> DB
    API3 --> DB
    
    DB --> Replica1[(Read Replica 1)]
    DB --> Replica2[(Read Replica 2)]
    
    API1 --> Analytics[(Analytics DB)]
    API2 --> Analytics
    API3 --> Analytics
    
    subgraph "URL Encoding Service"
        Encoder[Base62 Encoder]
        Counter[Distributed Counter]
    end
    
    API1 --> Encoder
    API2 --> Encoder
    API3 --> Encoder
```

## 🔧 Detailed Design

### 1. URL Encoding Strategy

**Base62 Encoding Approach:**
```
Characters: [a-z, A-Z, 0-9] = 62 characters
Short URL length: 7 characters
Total combinations: 62^7 = ~3.5 trillion URLs
```

**Algorithm:**
1. Generate unique ID (auto-increment or distributed counter)
2. Convert ID to Base62 string
3. Use first 7 characters as short URL

### 2. Database Schema

**URLs Table:**
```sql
CREATE TABLE urls (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    short_url VARCHAR(7) UNIQUE NOT NULL,
    long_url TEXT NOT NULL,
    user_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    INDEX idx_short_url (short_url),
    INDEX idx_user_id (user_id)
);
```

**Analytics Table:**
```sql
CREATE TABLE url_analytics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    short_url VARCHAR(7),
    clicked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    referer TEXT,
    INDEX idx_short_url_time (short_url, clicked_at)
);
```

### 3. API Design

**Create Short URL:**
```http
POST /api/v1/shorten
Content-Type: application/json

{
    "long_url": "https://example.com/very/long/url",
    "custom_alias": "my-link",  // optional
    "expires_at": "2024-12-31T23:59:59Z"  // optional
}

Response:
{
    "short_url": "https://short.ly/abc123",
    "long_url": "https://example.com/very/long/url",
    "created_at": "2024-01-15T10:30:00Z",
    "expires_at": "2024-12-31T23:59:59Z"
}
```

**Redirect URL:**
```http
GET /abc123

Response: 301 Redirect
Location: https://example.com/very/long/url
```

**Get Analytics:**
```http
GET /api/v1/analytics/abc123

Response:
{
    "short_url": "abc123",
    "total_clicks": 1250,
    "clicks_today": 45,
    "top_referrers": [...],
    "click_history": [...]
}
```

### 4. Caching Strategy

**Cache Layers:**
1. **Application Cache**: In-memory cache for frequently accessed URLs
2. **Redis Cache**: Distributed cache for URL mappings
3. **CDN**: Cache redirect responses globally

**Cache Pattern:**
```
1. Check application cache
2. If miss, check Redis cache
3. If miss, query database
4. Update caches with result
```

## ⚡ Scalability Considerations

### Database Scaling
1. **Read Replicas**: Handle read-heavy workload (10:1 ratio)
2. **Sharding**: Partition data by short_url hash
3. **Connection Pooling**: Manage database connections efficiently

### Application Scaling
1. **Horizontal Scaling**: Add more API servers behind load balancer
2. **Stateless Design**: No server-side sessions
3. **Microservices**: Separate URL shortening and analytics services

### Caching Optimization
1. **Cache Warming**: Pre-populate cache with popular URLs
2. **TTL Strategy**: Different TTL for different URL patterns
3. **Cache Invalidation**: Handle expired URLs gracefully

## 🛡️ Security & Reliability

### Security Measures
1. **Rate Limiting**: Prevent abuse (100 requests/hour per IP)
2. **URL Validation**: Validate and sanitize input URLs
3. **Blacklist**: Block malicious domains
4. **Authentication**: Optional user accounts for management

### Reliability Features
1. **Health Checks**: Monitor service health
2. **Circuit Breaker**: Prevent cascade failures
3. **Graceful Degradation**: Serve from cache when DB is down
4. **Monitoring**: Track key metrics (latency, error rates, throughput)

## 📊 Monitoring & Analytics

### Key Metrics
- **Request Rate**: Requests per second
- **Response Time**: P95, P99 latency
- **Error Rate**: 4xx, 5xx error percentage
- **Cache Hit Rate**: Cache effectiveness
- **Database Performance**: Query time, connection pool usage

### Alerting
- High error rate (> 1%)
- High latency (> 200ms P95)
- Low cache hit rate (< 80%)
- Database connection issues

## 🔄 Alternative Approaches

### 1. Hash-based Approach
- Use MD5/SHA-256 hash of URL
- Take first 7 characters
- Handle collisions with additional characters

### 2. UUID Approach
- Generate UUID for each URL
- Use Base62 encoding of UUID
- No collision handling needed

### 3. Distributed ID Generation
- Use Twitter Snowflake algorithm
- Timestamp + Machine ID + Sequence
- Guarantees uniqueness across distributed systems

## 🎯 Trade-offs & Decisions

| Aspect | Decision | Trade-off |
|--------|----------|-----------|
| Encoding | Base62 with counter | Simple but needs distributed counter |
| Database | MySQL with replicas | ACID compliance vs NoSQL flexibility |
| Caching | Redis + Application cache | Memory usage vs performance |
| Analytics | Separate service | Complexity vs performance isolation |

This URL shortener design provides a solid foundation that can handle millions of requests while maintaining high availability and performance.
