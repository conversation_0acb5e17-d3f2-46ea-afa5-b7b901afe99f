# Video Streaming Platform System Design

Design a video streaming platform like YouTube, Netflix, or Twitch that supports video upload, processing, storage, and streaming to millions of users.

## 📋 Requirements

### Functional Requirements
1. **Video Upload**: Users can upload videos in various formats
2. **Video Processing**: Transcode videos to multiple resolutions and formats
3. **Video Streaming**: Stream videos with adaptive bitrate
4. **Video Management**: CRUD operations for video metadata
5. **Search & Discovery**: Search videos by title, tags, description
6. **User Interactions**: Like, comment, subscribe, view history
7. **Live Streaming**: Support real-time video streaming

### Non-Functional Requirements
1. **Scale**: 1B hours watched per day, 500 hours uploaded per minute
2. **Global Reach**: Low latency worldwide (< 200ms)
3. **Availability**: 99.9% uptime
4. **Storage**: Petabytes of video content
5. **Bandwidth**: Handle peak traffic of 100 Gbps+

## 🔢 Capacity Estimation

### Traffic Estimates
- **Daily video uploads**: 500 hours/minute × 60 × 24 = 720,000 hours/day
- **Daily video views**: 1B hours watched/day
- **Read:Write ratio**: ~1000:1 (heavy read workload)
- **Concurrent viewers**: 50M peak concurrent users

### Storage Estimates
- **Average video**: 50MB per minute
- **Daily upload storage**: 720,000 hours × 60 min × 50MB = ~2.16PB/day
- **Multiple formats**: 5 different resolutions = ~10.8PB/day
- **Annual storage**: ~4,000PB per year

### Bandwidth Estimates
- **Upload bandwidth**: 720,000 hours/day × 50MB/min ÷ (24×60) = ~25GB/minute
- **Streaming bandwidth**: 1B hours × 5MB/min ÷ (24×60) = ~3.5TB/minute
- **Peak streaming**: 50M users × 5MB/min = ~250TB/minute

## 🏗️ High-Level Architecture

```mermaid
graph TB
    subgraph "Client Applications"
        WebClient[Web Browser<br/>HTML5 Video Player]
        MobileApp[Mobile Apps<br/>iOS/Android]
        SmartTV[Smart TV Apps<br/>Roku/Apple TV]
        GameConsole[Game Consoles<br/>PlayStation/Xbox]
    end

    subgraph "Global CDN Network"
        CDN1[CDN Edge Servers<br/>North America]
        CDN2[CDN Edge Servers<br/>Europe]
        CDN3[CDN Edge Servers<br/>Asia Pacific]
        CDNOrigin[CDN Origin Servers]
    end

    subgraph "Load Balancing & API Gateway"
        GlobalLB[Global Load Balancer<br/>DNS-based Routing]
        RegionalLB[Regional Load Balancers]
        APIGateway[API Gateway<br/>Rate Limiting & Auth]
    end

    subgraph "Core Application Services"
        VideoService[Video Service<br/>CRUD Operations]
        UserService[User Service<br/>Profiles & Auth]
        SearchService[Search Service<br/>Elasticsearch]
        RecommendationService[Recommendation Service<br/>ML-based]
        AnalyticsService[Analytics Service<br/>View Tracking]
    end

    subgraph "Video Processing Pipeline"
        UploadService[Upload Service<br/>Multipart Upload]
        TranscodingService[Transcoding Service<br/>FFmpeg Cluster]
        ThumbnailService[Thumbnail Generator<br/>Multiple Resolutions]
        QualityCheck[Quality Assurance<br/>Content Validation]
        SubtitleService[Subtitle Service<br/>Auto-generation]
    end

    subgraph "Message Queue System"
        VideoProcessingQueue[Video Processing Queue<br/>Apache Kafka]
        NotificationQueue[Notification Queue<br/>User Updates]
        AnalyticsQueue[Analytics Queue<br/>View Events]
    end

    subgraph "Storage Systems"
        VideoStorage[(Video Storage<br/>Distributed File System)]
        MetadataDB[(Metadata Database<br/>PostgreSQL Cluster)]
        UserDB[(User Database<br/>MySQL Cluster)]
        SearchIndex[(Search Index<br/>Elasticsearch)]
        AnalyticsDB[(Analytics Database<br/>ClickHouse)]
        Cache[Distributed Cache<br/>Redis Cluster]
    end

    subgraph "Machine Learning & Analytics"
        MLPipeline[ML Training Pipeline<br/>Recommendation Models]
        RealtimeAnalytics[Real-time Analytics<br/>Apache Storm]
        DataWarehouse[(Data Warehouse<br/>BigQuery/Snowflake)]
    end

    subgraph "External Services"
        PaymentGateway[Payment Gateway<br/>Stripe/PayPal]
        EmailService[Email Service<br/>SendGrid]
        PushNotification[Push Notifications<br/>FCM/APNS]
        ContentModeration[Content Moderation<br/>AI Services]
    end

    %% Client to CDN connections
    WebClient --> CDN1
    MobileApp --> CDN2
    SmartTV --> CDN3
    GameConsole --> CDN1

    %% CDN to Load Balancer
    CDN1 --> GlobalLB
    CDN2 --> GlobalLB
    CDN3 --> GlobalLB
    CDNOrigin --> VideoStorage

    %% Load Balancing Flow
    GlobalLB --> RegionalLB
    RegionalLB --> APIGateway

    %% API Gateway to Services
    APIGateway --> VideoService
    APIGateway --> UserService
    APIGateway --> SearchService
    APIGateway --> RecommendationService
    APIGateway --> AnalyticsService

    %% Video Processing Flow
    VideoService --> UploadService
    UploadService --> VideoProcessingQueue
    VideoProcessingQueue --> TranscodingService
    VideoProcessingQueue --> ThumbnailService
    VideoProcessingQueue --> QualityCheck
    VideoProcessingQueue --> SubtitleService

    %% Storage Connections
    TranscodingService --> VideoStorage
    VideoService --> MetadataDB
    UserService --> UserDB
    SearchService --> SearchIndex
    AnalyticsService --> AnalyticsDB

    %% Caching
    VideoService --> Cache
    UserService --> Cache
    SearchService --> Cache

    %% Analytics Flow
    AnalyticsService --> AnalyticsQueue
    AnalyticsQueue --> RealtimeAnalytics
    RealtimeAnalytics --> DataWarehouse

    %% ML Pipeline
    DataWarehouse --> MLPipeline
    MLPipeline --> RecommendationService

    %% External Services
    UserService --> PaymentGateway
    UserService --> EmailService
    AnalyticsService --> PushNotification
    UploadService --> ContentModeration

    %% Notifications
    VideoProcessingQueue --> NotificationQueue
    NotificationQueue --> PushNotification
    NotificationQueue --> EmailService

    classDef client fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef cdn fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef loadbalancer fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef service fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef processing fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef queue fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef storage fill:#e0f2f1,stroke:#00796b,stroke-width:2px
    classDef ml fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
    classDef external fill:#fff8e1,stroke:#ffa000,stroke-width:2px

    class WebClient,MobileApp,SmartTV,GameConsole client
    class CDN1,CDN2,CDN3,CDNOrigin cdn
    class GlobalLB,RegionalLB,APIGateway loadbalancer
    class VideoService,UserService,SearchService,RecommendationService,AnalyticsService service
    class UploadService,TranscodingService,ThumbnailService,QualityCheck,SubtitleService processing
    class VideoProcessingQueue,NotificationQueue,AnalyticsQueue queue
    class VideoStorage,MetadataDB,UserDB,SearchIndex,AnalyticsDB,Cache storage
    class MLPipeline,RealtimeAnalytics,DataWarehouse ml
    class PaymentGateway,EmailService,PushNotification,ContentModeration external
```

## 🔧 Detailed Design

### 1. Video Upload Flow

**Upload Process:**
```java
@RestController
public class VideoUploadController {
    
    @PostMapping("/api/v1/videos/upload")
    public ResponseEntity<VideoUploadResponse> uploadVideo(
            @RequestParam("file") MultipartFile file,
            @RequestParam("title") String title,
            @RequestParam("description") String description,
            @RequestHeader("Authorization") String token) {
        
        // Validate user and file
        User user = authService.validateToken(token);
        validateVideoFile(file);
        
        // Generate unique video ID
        String videoId = UUID.randomUUID().toString();
        
        // Upload to temporary storage
        String tempUrl = uploadToTempStorage(file, videoId);
        
        // Create video metadata
        Video video = Video.builder()
            .videoId(videoId)
            .userId(user.getId())
            .title(title)
            .description(description)
            .status(VideoStatus.PROCESSING)
            .uploadedAt(Instant.now())
            .build();
        
        videoRepository.save(video);
        
        // Queue for processing
        videoProcessingQueue.send(new VideoProcessingMessage(videoId, tempUrl));
        
        return ResponseEntity.ok(new VideoUploadResponse(videoId, "Upload successful"));
    }
}
```

### 2. Video Processing Pipeline

**Transcoding Service:**
```java
@Service
public class VideoTranscodingService {
    
    @EventListener
    public void processVideo(VideoProcessingMessage message) {
        try {
            String videoId = message.getVideoId();
            String sourceUrl = message.getSourceUrl();
            
            // Download source video
            File sourceFile = downloadVideo(sourceUrl);
            
            // Generate multiple resolutions
            List<TranscodingJob> jobs = createTranscodingJobs(videoId, sourceFile);
            
            // Process in parallel
            List<CompletableFuture<TranscodedVideo>> futures = jobs.stream()
                .map(job -> CompletableFuture.supplyAsync(() -> transcodeVideo(job)))
                .collect(Collectors.toList());
            
            // Wait for all transcoding to complete
            List<TranscodedVideo> transcodedVideos = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
            
            // Upload transcoded videos to storage
            uploadTranscodedVideos(videoId, transcodedVideos);
            
            // Generate thumbnails
            generateThumbnails(videoId, sourceFile);
            
            // Update video status
            updateVideoStatus(videoId, VideoStatus.READY);
            
            // Send notification
            notificationService.notifyVideoReady(videoId);
            
        } catch (Exception e) {
            handleProcessingError(message.getVideoId(), e);
        }
    }
    
    private List<TranscodingJob> createTranscodingJobs(String videoId, File sourceFile) {
        return Arrays.asList(
            new TranscodingJob(videoId, sourceFile, "240p", "400k"),
            new TranscodingJob(videoId, sourceFile, "360p", "800k"),
            new TranscodingJob(videoId, sourceFile, "480p", "1200k"),
            new TranscodingJob(videoId, sourceFile, "720p", "2500k"),
            new TranscodingJob(videoId, sourceFile, "1080p", "5000k")
        );
    }
}
```

### 3. Video Streaming

**Adaptive Bitrate Streaming:**
```java
@RestController
public class VideoStreamingController {
    
    @GetMapping("/api/v1/videos/{videoId}/stream")
    public ResponseEntity<Resource> streamVideo(
            @PathVariable String videoId,
            @RequestHeader(value = "Range", required = false) String rangeHeader,
            HttpServletRequest request) {
        
        // Get video metadata
        Video video = videoService.getVideo(videoId);
        if (video == null || video.getStatus() != VideoStatus.READY) {
            return ResponseEntity.notFound().build();
        }
        
        // Determine best quality based on client capabilities
        String quality = determineOptimalQuality(request);
        
        // Get video file URL
        String videoUrl = getVideoUrl(videoId, quality);
        
        // Handle range requests for video seeking
        if (rangeHeader != null) {
            return handleRangeRequest(videoUrl, rangeHeader);
        }
        
        // Record view analytics
        analyticsService.recordView(videoId, request);
        
        // Return video stream
        Resource videoResource = resourceLoader.getResource(videoUrl);
        return ResponseEntity.ok()
            .contentType(MediaType.parseMediaType("video/mp4"))
            .body(videoResource);
    }
    
    private String determineOptimalQuality(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        String connection = request.getHeader("Connection-Type");
        
        // Simple quality selection logic
        if (isMobileDevice(userAgent) || isSlowConnection(connection)) {
            return "480p";
        } else if (isHighEndDevice(userAgent)) {
            return "1080p";
        }
        return "720p";
    }
}
```

### 4. Database Schema

**Videos Table:**
```sql
CREATE TABLE videos (
    video_id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    duration_seconds INT,
    file_size_bytes BIGINT,
    status ENUM('PROCESSING', 'READY', 'FAILED') DEFAULT 'PROCESSING',
    privacy ENUM('PUBLIC', 'UNLISTED', 'PRIVATE') DEFAULT 'PUBLIC',
    view_count BIGINT DEFAULT 0,
    like_count INT DEFAULT 0,
    dislike_count INT DEFAULT 0,
    comment_count INT DEFAULT 0,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    published_at TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_published_at (published_at),
    FULLTEXT idx_title_description (title, description)
);
```

**Video Files Table:**
```sql
CREATE TABLE video_files (
    file_id VARCHAR(36) PRIMARY KEY,
    video_id VARCHAR(36) NOT NULL,
    quality VARCHAR(10) NOT NULL, -- 240p, 360p, 480p, 720p, 1080p
    format VARCHAR(10) NOT NULL,  -- mp4, webm
    file_url VARCHAR(500) NOT NULL,
    file_size_bytes BIGINT,
    bitrate_kbps INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_video_id (video_id),
    UNIQUE KEY unique_video_quality (video_id, quality, format)
);
```

### 5. Content Delivery Network (CDN)

**CDN Configuration:**
```yaml
# CDN configuration for video streaming
cdn_config:
  providers:
    - name: "primary_cdn"
      regions: ["us-east", "us-west", "eu-west", "asia-pacific"]
      cache_ttl: 86400  # 24 hours
      
  video_delivery:
    adaptive_streaming: true
    protocols: ["HLS", "DASH"]
    edge_caching: true
    
  optimization:
    compression: true
    image_optimization: true
    prefetch_popular_content: true
```

**CDN Integration:**
```java
@Service
public class CDNService {
    
    public String getVideoStreamUrl(String videoId, String quality) {
        // Get CDN endpoint closest to user
        String cdnEndpoint = getCdnEndpoint(getCurrentUserRegion());
        
        // Generate signed URL with expiration
        String signedUrl = generateSignedUrl(cdnEndpoint, videoId, quality);
        
        return signedUrl;
    }
    
    private String generateSignedUrl(String endpoint, String videoId, String quality) {
        long expiration = System.currentTimeMillis() + TimeUnit.HOURS.toMillis(1);
        String signature = hmacSha256(videoId + quality + expiration, secretKey);
        
        return String.format("%s/videos/%s/%s.mp4?expires=%d&signature=%s",
            endpoint, videoId, quality, expiration, signature);
    }
}
```

### 6. Search and Recommendation

**Search Service:**
```java
@Service
public class VideoSearchService {
    private final ElasticsearchClient elasticsearchClient;
    
    public SearchResponse<Video> searchVideos(String query, int page, int size) {
        SearchRequest request = SearchRequest.of(s -> s
            .index("videos")
            .query(q -> q
                .multiMatch(m -> m
                    .query(query)
                    .fields("title^2", "description", "tags")
                    .type(TextQueryType.BestFields)
                )
            )
            .highlight(h -> h
                .fields("title", f -> f)
                .fields("description", f -> f)
            )
            .from(page * size)
            .size(size)
            .sort(so -> so
                .field(f -> f.field("view_count").order(SortOrder.Desc))
            )
        );
        
        return elasticsearchClient.search(request, Video.class);
    }
}
```

**Recommendation Service:**
```java
@Service
public class VideoRecommendationService {
    
    public List<Video> getRecommendations(String userId, int count) {
        // Get user's viewing history
        List<String> viewedVideoIds = getViewingHistory(userId);
        
        // Get user preferences
        UserPreferences preferences = getUserPreferences(userId);
        
        // Collaborative filtering
        List<String> similarUsers = findSimilarUsers(userId);
        List<Video> collaborativeRecommendations = getPopularVideosFromSimilarUsers(similarUsers);
        
        // Content-based filtering
        List<Video> contentBasedRecommendations = getVideosBasedOnPreferences(preferences);
        
        // Combine and rank recommendations
        return combineAndRankRecommendations(
            collaborativeRecommendations, 
            contentBasedRecommendations, 
            count
        );
    }
}
```

## ⚡ Scalability Solutions

### 1. Video Storage Optimization

**Tiered Storage Strategy:**
```java
@Service
public class VideoStorageService {
    
    public void optimizeVideoStorage(String videoId) {
        VideoMetrics metrics = getVideoMetrics(videoId);
        
        if (metrics.getViewCount() > 1_000_000) {
            // Hot content - keep in fast SSD storage
            moveToHotStorage(videoId);
        } else if (metrics.getDaysSinceLastView() > 30) {
            // Cold content - move to cheaper storage
            moveToColdStorage(videoId);
        } else if (metrics.getDaysSinceLastView() > 365) {
            // Archive content - move to archival storage
            moveToArchivalStorage(videoId);
        }
    }
}
```

### 2. Database Sharding

**Video Metadata Sharding:**
```
Shard by video_id hash:
- Shard 1: video_id hash % 8 == 0
- Shard 2: video_id hash % 8 == 1
- ...
- Shard 8: video_id hash % 8 == 7

User data sharded by user_id:
- Ensures user's videos are co-located
- Optimizes user dashboard queries
```

### 3. Caching Strategy

**Multi-Level Caching:**
```java
@Service
public class VideoCacheService {
    private final RedisTemplate<String, Object> redisTemplate;
    private final Cache<String, Video> localCache;
    
    public Video getVideo(String videoId) {
        // L1: Local cache
        Video video = localCache.getIfPresent(videoId);
        if (video != null) {
            return video;
        }
        
        // L2: Redis cache
        video = (Video) redisTemplate.opsForValue().get("video:" + videoId);
        if (video != null) {
            localCache.put(videoId, video);
            return video;
        }
        
        // L3: Database
        video = videoRepository.findById(videoId);
        if (video != null) {
            redisTemplate.opsForValue().set("video:" + videoId, video, Duration.ofHours(1));
            localCache.put(videoId, video);
        }
        
        return video;
    }
}
```

## 📊 Monitoring & Analytics

### Real-time Analytics
```java
@Service
public class VideoAnalyticsService {
    
    @EventListener
    public void onVideoView(VideoViewEvent event) {
        // Real-time metrics
        incrementViewCount(event.getVideoId());
        updateWatchTime(event.getVideoId(), event.getWatchDuration());
        
        // User behavior tracking
        trackUserEngagement(event.getUserId(), event.getVideoId());
        
        // Geographic analytics
        updateGeographicMetrics(event.getVideoId(), event.getUserLocation());
        
        // Quality metrics
        trackVideoQuality(event.getVideoId(), event.getQuality());
    }
}
```

This video streaming platform design provides a scalable, global solution capable of handling billions of video views while maintaining high quality and low latency streaming experience.
