# API Rate Limiter System Design

Design a rate limiting system that controls the rate of traffic sent by clients to protect APIs from abuse and ensure fair usage.

## 📋 Requirements

### Functional Requirements
1. **Rate Limiting**: Limit requests per user/IP within time windows
2. **Multiple Algorithms**: Support different rate limiting algorithms
3. **Flexible Rules**: Different limits for different APIs/users
4. **Distributed**: Work across multiple servers
5. **Real-time**: Low latency decision making (< 1ms)
6. **Monitoring**: Track rate limit violations and usage patterns

### Non-Functional Requirements
1. **Scale**: Handle 1M requests per second
2. **Latency**: < 1ms for rate limit check
3. **Availability**: 99.99% uptime
4. **Accuracy**: Minimal false positives/negatives
5. **Memory Efficient**: Optimize memory usage for large user base

## 🔢 Capacity Estimation

### Traffic Estimates
- **Peak requests**: 1M requests/second
- **Active users**: 10M users
- **API endpoints**: 1000 different endpoints
- **Rate limit checks**: 1M checks/second

### Memory Estimates
- **Per user state**: ~100 bytes (counters + metadata)
- **Total memory**: 10M users × 100 bytes = ~1GB
- **Redis cluster**: 3-5 nodes for redundancy

## 🏗️ High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        Client1[Client 1]
        Client2[Client 2]
        ClientN[Client N]
    end
    
    subgraph "API Gateway Layer"
        Gateway1[API Gateway 1]
        Gateway2[API Gateway 2]
        Gateway3[API Gateway 3]
    end
    
    subgraph "Rate Limiter Service"
        RateLimiter1[Rate Limiter 1]
        RateLimiter2[Rate Limiter 2]
        RateLimiter3[Rate Limiter 3]
    end
    
    subgraph "Distributed Cache"
        Redis1[(Redis Node 1)]
        Redis2[(Redis Node 2)]
        Redis3[(Redis Node 3)]
    end
    
    subgraph "Configuration"
        ConfigDB[(Config Database)]
        RuleEngine[Rule Engine]
    end
    
    subgraph "Monitoring"
        Metrics[Metrics Service]
        Alerts[Alert System]
    end
    
    Client1 --> Gateway1
    Client2 --> Gateway2
    ClientN --> Gateway3
    
    Gateway1 --> RateLimiter1
    Gateway2 --> RateLimiter2
    Gateway3 --> RateLimiter3
    
    RateLimiter1 --> Redis1
    RateLimiter2 --> Redis2
    RateLimiter3 --> Redis3
    
    RateLimiter1 --> ConfigDB
    RateLimiter2 --> ConfigDB
    RateLimiter3 --> ConfigDB
    
    RateLimiter1 --> Metrics
    RateLimiter2 --> Metrics
    RateLimiter3 --> Metrics
```

## 🔧 Rate Limiting Algorithms

### 1. Token Bucket Algorithm

**Implementation:**
```java
public class TokenBucketRateLimiter {
    private final int capacity;
    private final int refillRate;
    private int tokens;
    private long lastRefillTime;
    
    public TokenBucketRateLimiter(int capacity, int refillRate) {
        this.capacity = capacity;
        this.refillRate = refillRate;
        this.tokens = capacity;
        this.lastRefillTime = System.currentTimeMillis();
    }
    
    public synchronized boolean allowRequest() {
        refillTokens();
        
        if (tokens > 0) {
            tokens--;
            return true;
        }
        return false;
    }
    
    private void refillTokens() {
        long now = System.currentTimeMillis();
        long timePassed = now - lastRefillTime;
        int tokensToAdd = (int) (timePassed * refillRate / 1000);
        
        tokens = Math.min(capacity, tokens + tokensToAdd);
        lastRefillTime = now;
    }
}
```

**Redis Implementation:**
```lua
-- Token bucket algorithm in Redis Lua script
local key = KEYS[1]
local capacity = tonumber(ARGV[1])
local refill_rate = tonumber(ARGV[2])
local current_time = tonumber(ARGV[3])

local bucket = redis.call('HMGET', key, 'tokens', 'last_refill')
local tokens = tonumber(bucket[1]) or capacity
local last_refill = tonumber(bucket[2]) or current_time

-- Calculate tokens to add
local time_passed = current_time - last_refill
local tokens_to_add = math.floor(time_passed * refill_rate / 1000)
tokens = math.min(capacity, tokens + tokens_to_add)

-- Check if request is allowed
if tokens > 0 then
    tokens = tokens - 1
    redis.call('HMSET', key, 'tokens', tokens, 'last_refill', current_time)
    redis.call('EXPIRE', key, 3600)
    return 1
else
    redis.call('HMSET', key, 'tokens', tokens, 'last_refill', current_time)
    redis.call('EXPIRE', key, 3600)
    return 0
end
```

### 2. Sliding Window Log Algorithm

**Implementation:**
```java
public class SlidingWindowLogRateLimiter {
    private final int maxRequests;
    private final long windowSizeMs;
    private final Queue<Long> requestLog;
    
    public SlidingWindowLogRateLimiter(int maxRequests, long windowSizeMs) {
        this.maxRequests = maxRequests;
        this.windowSizeMs = windowSizeMs;
        this.requestLog = new LinkedList<>();
    }
    
    public synchronized boolean allowRequest() {
        long now = System.currentTimeMillis();
        
        // Remove old requests outside the window
        while (!requestLog.isEmpty() && requestLog.peek() <= now - windowSizeMs) {
            requestLog.poll();
        }
        
        // Check if we can allow the request
        if (requestLog.size() < maxRequests) {
            requestLog.offer(now);
            return true;
        }
        
        return false;
    }
}
```

### 3. Sliding Window Counter Algorithm

**Redis Implementation:**
```lua
-- Sliding window counter algorithm
local key = KEYS[1]
local window_size = tonumber(ARGV[1])
local max_requests = tonumber(ARGV[2])
local current_time = tonumber(ARGV[3])

local window_start = current_time - window_size
local current_count = redis.call('ZCOUNT', key, window_start, current_time)

if current_count < max_requests then
    redis.call('ZADD', key, current_time, current_time)
    redis.call('ZREMRANGEBYSCORE', key, 0, window_start)
    redis.call('EXPIRE', key, math.ceil(window_size / 1000))
    return 1
else
    return 0
end
```

### 4. Fixed Window Counter Algorithm

**Implementation:**
```java
public class FixedWindowRateLimiter {
    private final int maxRequests;
    private final long windowSizeMs;
    private int requestCount;
    private long windowStart;
    
    public FixedWindowRateLimiter(int maxRequests, long windowSizeMs) {
        this.maxRequests = maxRequests;
        this.windowSizeMs = windowSizeMs;
        this.requestCount = 0;
        this.windowStart = System.currentTimeMillis();
    }
    
    public synchronized boolean allowRequest() {
        long now = System.currentTimeMillis();
        
        // Check if we need to reset the window
        if (now - windowStart >= windowSizeMs) {
            requestCount = 0;
            windowStart = now;
        }
        
        if (requestCount < maxRequests) {
            requestCount++;
            return true;
        }
        
        return false;
    }
}
```

## 🔧 Detailed Design

### 1. Rate Limiter Service

**Main Service Class:**
```java
@Service
public class RateLimiterService {
    private final RedisTemplate<String, String> redisTemplate;
    private final RateLimitConfigService configService;
    private final MetricsService metricsService;
    
    public RateLimitResult checkRateLimit(RateLimitRequest request) {
        // Get rate limit rules for the request
        List<RateLimitRule> rules = configService.getRules(request);
        
        for (RateLimitRule rule : rules) {
            RateLimitResult result = applyRule(request, rule);
            if (!result.isAllowed()) {
                metricsService.recordRateLimitViolation(request, rule);
                return result;
            }
        }
        
        return RateLimitResult.allowed();
    }
    
    private RateLimitResult applyRule(RateLimitRequest request, RateLimitRule rule) {
        String key = generateKey(request, rule);
        
        switch (rule.getAlgorithm()) {
            case TOKEN_BUCKET:
                return applyTokenBucket(key, rule);
            case SLIDING_WINDOW:
                return applySlidingWindow(key, rule);
            case FIXED_WINDOW:
                return applyFixedWindow(key, rule);
            default:
                throw new IllegalArgumentException("Unknown algorithm: " + rule.getAlgorithm());
        }
    }
}
```

### 2. Configuration Management

**Rate Limit Rules:**
```java
public class RateLimitRule {
    private String id;
    private String name;
    private RateLimitAlgorithm algorithm;
    private int maxRequests;
    private long windowSizeMs;
    private List<String> applicableEndpoints;
    private List<String> applicableUserTiers;
    private int priority;
    
    // Getters and setters
}
```

**Configuration Database Schema:**
```sql
CREATE TABLE rate_limit_rules (
    rule_id VARCHAR(36) PRIMARY KEY,
    rule_name VARCHAR(255) NOT NULL,
    algorithm ENUM('TOKEN_BUCKET', 'SLIDING_WINDOW', 'FIXED_WINDOW'),
    max_requests INT NOT NULL,
    window_size_ms BIGINT NOT NULL,
    applicable_endpoints JSON,
    applicable_user_tiers JSON,
    priority INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3. Key Generation Strategy

**Hierarchical Key Structure:**
```java
public class RateLimitKeyGenerator {
    public String generateKey(RateLimitRequest request, RateLimitRule rule) {
        StringBuilder keyBuilder = new StringBuilder();
        
        // Base prefix
        keyBuilder.append("rate_limit:");
        
        // Rule identifier
        keyBuilder.append(rule.getId()).append(":");
        
        // User/IP identifier
        if (request.getUserId() != null) {
            keyBuilder.append("user:").append(request.getUserId());
        } else {
            keyBuilder.append("ip:").append(request.getIpAddress());
        }
        
        // Endpoint identifier (optional)
        if (rule.isEndpointSpecific()) {
            keyBuilder.append(":endpoint:").append(request.getEndpoint());
        }
        
        // Time window (for fixed window algorithms)
        if (rule.getAlgorithm() == RateLimitAlgorithm.FIXED_WINDOW) {
            long windowStart = System.currentTimeMillis() / rule.getWindowSizeMs();
            keyBuilder.append(":window:").append(windowStart);
        }
        
        return keyBuilder.toString();
    }
}
```

### 4. Distributed Rate Limiting

**Consistent Hashing for Redis:**
```java
@Component
public class DistributedRateLimiter {
    private final List<RedisTemplate<String, String>> redisNodes;
    private final ConsistentHash<RedisTemplate<String, String>> consistentHash;
    
    public DistributedRateLimiter(List<RedisTemplate<String, String>> redisNodes) {
        this.redisNodes = redisNodes;
        this.consistentHash = new ConsistentHash<>(redisNodes);
    }
    
    public RateLimitResult checkRateLimit(String key, RateLimitRule rule) {
        RedisTemplate<String, String> redisNode = consistentHash.get(key);
        return executeRateLimitCheck(redisNode, key, rule);
    }
    
    private RateLimitResult executeRateLimitCheck(RedisTemplate<String, String> redis, 
                                                  String key, RateLimitRule rule) {
        // Execute Lua script for atomic rate limit check
        DefaultRedisScript<Long> script = new DefaultRedisScript<>();
        script.setScriptText(getLuaScript(rule.getAlgorithm()));
        script.setResultType(Long.class);
        
        Long result = redis.execute(script, 
            Collections.singletonList(key),
            String.valueOf(rule.getMaxRequests()),
            String.valueOf(rule.getWindowSizeMs()),
            String.valueOf(System.currentTimeMillis())
        );
        
        return result == 1 ? RateLimitResult.allowed() : RateLimitResult.denied();
    }
}
```

## ⚡ Performance Optimizations

### 1. Local Cache Layer

**In-Memory Cache for Hot Keys:**
```java
@Component
public class LocalCacheRateLimiter {
    private final Cache<String, TokenBucket> localCache;
    private final DistributedRateLimiter distributedLimiter;
    
    public LocalCacheRateLimiter() {
        this.localCache = Caffeine.newBuilder()
            .maximumSize(10_000)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();
    }
    
    public RateLimitResult checkRateLimit(String key, RateLimitRule rule) {
        // Try local cache first for frequently accessed keys
        TokenBucket bucket = localCache.getIfPresent(key);
        if (bucket != null && bucket.allowRequest()) {
            return RateLimitResult.allowed();
        }
        
        // Fall back to distributed rate limiter
        return distributedLimiter.checkRateLimit(key, rule);
    }
}
```

### 2. Batch Processing

**Batch Rate Limit Checks:**
```java
public List<RateLimitResult> checkRateLimitBatch(List<RateLimitRequest> requests) {
    Map<String, List<RateLimitRequest>> groupedByNode = groupRequestsByRedisNode(requests);
    
    List<CompletableFuture<List<RateLimitResult>>> futures = new ArrayList<>();
    
    for (Map.Entry<String, List<RateLimitRequest>> entry : groupedByNode.entrySet()) {
        CompletableFuture<List<RateLimitResult>> future = CompletableFuture
            .supplyAsync(() -> processBatchOnNode(entry.getKey(), entry.getValue()));
        futures.add(future);
    }
    
    return futures.stream()
        .map(CompletableFuture::join)
        .flatMap(List::stream)
        .collect(Collectors.toList());
}
```

## 📊 Monitoring & Alerting

### Key Metrics
```java
@Component
public class RateLimiterMetrics {
    private final MeterRegistry meterRegistry;
    
    public void recordRateLimitCheck(String algorithm, boolean allowed) {
        Counter.builder("rate_limit_checks_total")
            .tag("algorithm", algorithm)
            .tag("result", allowed ? "allowed" : "denied")
            .register(meterRegistry)
            .increment();
    }
    
    public void recordLatency(String algorithm, long latencyMs) {
        Timer.builder("rate_limit_check_duration")
            .tag("algorithm", algorithm)
            .register(meterRegistry)
            .record(latencyMs, TimeUnit.MILLISECONDS);
    }
}
```

### Health Checks
```java
@Component
public class RateLimiterHealthCheck implements HealthIndicator {
    private final RedisTemplate<String, String> redisTemplate;
    
    @Override
    public Health health() {
        try {
            String result = redisTemplate.execute((RedisCallback<String>) connection -> {
                return connection.ping();
            });
            
            if ("PONG".equals(result)) {
                return Health.up()
                    .withDetail("redis", "Available")
                    .build();
            }
        } catch (Exception e) {
            return Health.down()
                .withDetail("redis", "Unavailable")
                .withException(e)
                .build();
        }
        
        return Health.down().build();
    }
}
```

## 🔄 Advanced Features

### Dynamic Rule Updates
```java
@EventListener
public void onRuleUpdated(RuleUpdatedEvent event) {
    // Invalidate local caches
    localCache.invalidateAll();
    
    // Notify all rate limiter instances
    messagingService.broadcast("rule_updated", event.getRule());
}
```

### Rate Limit Headers
```java
@Component
public class RateLimitHeadersFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        // Add rate limit headers
        httpResponse.setHeader("X-RateLimit-Limit", "1000");
        httpResponse.setHeader("X-RateLimit-Remaining", "999");
        httpResponse.setHeader("X-RateLimit-Reset", "**********");
        
        chain.doFilter(request, response);
    }
}
```

This rate limiter design provides a flexible, scalable solution that can handle millions of requests per second while maintaining low latency and high accuracy.
