# Distributed Key-Value Store System Design

Design a distributed key-value store like Amazon DynamoDB, Apache Cassandra, or Redis Cluster that provides high availability, scalability, and configurable consistency.

## 📋 Requirements

### Functional Requirements
1. **Basic Operations**: GET, PUT, DELETE operations
2. **Distributed Storage**: Data distributed across multiple nodes
3. **Replication**: Data replicated for fault tolerance
4. **Consistency Levels**: Configurable consistency (eventual, strong)
5. **Partitioning**: Automatic data partitioning and rebalancing
6. **Failure Handling**: Handle node failures gracefully
7. **Scalability**: Add/remove nodes dynamically

### Non-Functional Requirements
1. **Scale**: Store 100TB+ data, handle 1M ops/second
2. **Availability**: 99.99% uptime
3. **Latency**: < 1ms for reads, < 5ms for writes
4. **Consistency**: Tunable consistency levels
5. **Durability**: No data loss under normal failures

## 🔢 Capacity Estimation

### Storage Estimates
- **Total data**: 100TB across cluster
- **Average key size**: 64 bytes
- **Average value size**: 1KB
- **Total keys**: ~100B keys
- **Replication factor**: 3x = 300TB total storage

### Traffic Estimates
- **Read operations**: 800K ops/second
- **Write operations**: 200K ops/second
- **Peak traffic**: 2x average = 2M ops/second

### Node Estimates
- **Storage per node**: 1TB SSD
- **Required nodes**: 300 nodes (with replication)
- **Nodes per rack**: 20 nodes
- **Total racks**: 15 racks

## 🏗️ High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        Client1[Client Application 1]
        Client2[Client Application 2]
        ClientN[Client Application N]
    end
    
    subgraph "Load Balancing"
        LB[Load Balancer]
        Proxy[Proxy Layer]
    end
    
    subgraph "Cluster Management"
        CoordinatorService[Coordinator Service]
        MembershipService[Membership Service]
        FailureDetector[Failure Detector]
        ConfigService[Configuration Service]
    end
    
    subgraph "Data Nodes - Ring 1"
        Node1[Node 1<br/>Partition 1-100]
        Node2[Node 2<br/>Partition 101-200]
        Node3[Node 3<br/>Partition 201-300]
    end
    
    subgraph "Data Nodes - Ring 2"
        Node4[Node 4<br/>Replica Ring]
        Node5[Node 5<br/>Replica Ring]
        Node6[Node 6<br/>Replica Ring]
    end
    
    subgraph "Data Nodes - Ring 3"
        Node7[Node 7<br/>Replica Ring]
        Node8[Node 8<br/>Replica Ring]
        Node9[Node 9<br/>Replica Ring]
    end
    
    subgraph "Storage Engine"
        LSMTree[LSM Tree Storage]
        WAL[Write-Ahead Log]
        Compaction[Compaction Service]
        BloomFilter[Bloom Filters]
    end
    
    subgraph "Monitoring & Operations"
        MetricsCollector[Metrics Collector]
        HealthMonitor[Health Monitor]
        AlertManager[Alert Manager]
        BackupService[Backup Service]
    end
    
    Client1 --> LB
    Client2 --> LB
    ClientN --> LB
    
    LB --> Proxy
    Proxy --> CoordinatorService
    
    CoordinatorService --> MembershipService
    CoordinatorService --> FailureDetector
    CoordinatorService --> ConfigService
    
    CoordinatorService --> Node1
    CoordinatorService --> Node2
    CoordinatorService --> Node3
    CoordinatorService --> Node4
    CoordinatorService --> Node5
    CoordinatorService --> Node6
    CoordinatorService --> Node7
    CoordinatorService --> Node8
    CoordinatorService --> Node9
    
    Node1 --> LSMTree
    Node2 --> LSMTree
    Node3 --> LSMTree
    
    LSMTree --> WAL
    LSMTree --> Compaction
    LSMTree --> BloomFilter
    
    Node1 --> MetricsCollector
    Node2 --> MetricsCollector
    Node3 --> MetricsCollector
    
    MetricsCollector --> HealthMonitor
    HealthMonitor --> AlertManager
    
    Node1 --> BackupService
    Node2 --> BackupService
    Node3 --> BackupService
    
    classDef client fill:#e3f2fd
    classDef loadbalancer fill:#f3e5f5
    classDef management fill:#e8f5e8
    classDef datanode fill:#fff3e0
    classDef storage fill:#fce4ec
    classDef monitoring fill:#f1f8e9
    
    class Client1,Client2,ClientN client
    class LB,Proxy loadbalancer
    class CoordinatorService,MembershipService,FailureDetector,ConfigService management
    class Node1,Node2,Node3,Node4,Node5,Node6,Node7,Node8,Node9 datanode
    class LSMTree,WAL,Compaction,BloomFilter storage
    class MetricsCollector,HealthMonitor,AlertManager,BackupService monitoring
```

## 🔧 Detailed Design

### 1. Consistent Hashing and Partitioning

```java
public class ConsistentHashRing {
    private final TreeMap<Long, Node> ring = new TreeMap<>();
    private final int virtualNodes;
    private final int replicationFactor;
    
    public ConsistentHashRing(int virtualNodes, int replicationFactor) {
        this.virtualNodes = virtualNodes;
        this.replicationFactor = replicationFactor;
    }
    
    public void addNode(Node node) {
        for (int i = 0; i < virtualNodes; i++) {
            String virtualNodeKey = node.getId() + ":" + i;
            long hash = hash(virtualNodeKey);
            ring.put(hash, node);
        }
    }
    
    public void removeNode(Node node) {
        for (int i = 0; i < virtualNodes; i++) {
            String virtualNodeKey = node.getId() + ":" + i;
            long hash = hash(virtualNodeKey);
            ring.remove(hash);
        }
    }
    
    public List<Node> getPreferenceList(String key) {
        List<Node> preferenceList = new ArrayList<>();
        long hash = hash(key);
        
        Map.Entry<Long, Node> entry = ring.ceilingEntry(hash);
        if (entry == null) {
            entry = ring.firstEntry();
        }
        
        // Get N replicas (where N = replicationFactor)
        Iterator<Node> iterator = ring.tailMap(entry.getKey()).values().iterator();
        Set<Node> uniqueNodes = new LinkedHashSet<>();
        
        while (uniqueNodes.size() < replicationFactor && iterator.hasNext()) {
            uniqueNodes.add(iterator.next());
        }
        
        // Wrap around if needed
        if (uniqueNodes.size() < replicationFactor) {
            iterator = ring.values().iterator();
            while (uniqueNodes.size() < replicationFactor && iterator.hasNext()) {
                uniqueNodes.add(iterator.next());
            }
        }
        
        return new ArrayList<>(uniqueNodes);
    }
    
    private long hash(String key) {
        return Hashing.murmur3_128().hashString(key, StandardCharsets.UTF_8).asLong();
    }
}
```

### 2. Storage Engine with LSM Trees

```java
public class LSMTreeStorageEngine implements StorageEngine {
    
    private final MemTable memTable;
    private final List<SSTable> sstables;
    private final WriteAheadLog wal;
    private final CompactionManager compactionManager;
    private final BloomFilterManager bloomFilterManager;
    
    public LSMTreeStorageEngine(String dataDirectory) {
        this.memTable = new MemTable();
        this.sstables = new CopyOnWriteArrayList<>();
        this.wal = new WriteAheadLog(dataDirectory + "/wal");
        this.compactionManager = new CompactionManager(this);
        this.bloomFilterManager = new BloomFilterManager();
        
        // Start background compaction
        compactionManager.start();
    }
    
    @Override
    public void put(String key, byte[] value, long timestamp) {
        // Write to WAL first for durability
        wal.append(new LogEntry(LogEntry.Type.PUT, key, value, timestamp));
        
        // Write to memtable
        memTable.put(key, value, timestamp);
        
        // Check if memtable needs to be flushed
        if (memTable.size() > MEMTABLE_THRESHOLD) {
            flushMemTable();
        }
    }
    
    @Override
    public byte[] get(String key) {
        // Check memtable first
        byte[] value = memTable.get(key);
        if (value != null) {
            return value;
        }
        
        // Check SSTables in reverse chronological order
        for (int i = sstables.size() - 1; i >= 0; i--) {
            SSTable sstable = sstables.get(i);
            
            // Use bloom filter to avoid unnecessary disk reads
            if (!bloomFilterManager.mightContain(sstable.getId(), key)) {
                continue;
            }
            
            value = sstable.get(key);
            if (value != null) {
                return value;
            }
        }
        
        return null; // Key not found
    }
    
    @Override
    public void delete(String key, long timestamp) {
        // Write tombstone to WAL
        wal.append(new LogEntry(LogEntry.Type.DELETE, key, null, timestamp));
        
        // Write tombstone to memtable
        memTable.delete(key, timestamp);
        
        if (memTable.size() > MEMTABLE_THRESHOLD) {
            flushMemTable();
        }
    }
    
    private void flushMemTable() {
        if (memTable.isEmpty()) {
            return;
        }
        
        // Create new SSTable from memtable
        String sstableId = generateSSTableId();
        SSTable sstable = SSTable.create(sstableId, memTable.getEntries());
        
        // Create bloom filter for the new SSTable
        BloomFilter bloomFilter = bloomFilterManager.createBloomFilter(sstableId, memTable.getKeys());
        
        sstables.add(sstable);
        
        // Clear memtable and WAL
        memTable.clear();
        wal.truncate();
        
        // Trigger compaction if needed
        compactionManager.triggerCompactionIfNeeded();
    }
}

public class MemTable {
    private final ConcurrentSkipListMap<String, Entry> data = new ConcurrentSkipListMap<>();
    private final AtomicLong size = new AtomicLong(0);
    
    public void put(String key, byte[] value, long timestamp) {
        Entry entry = new Entry(value, timestamp, false);
        Entry oldEntry = data.put(key, entry);
        
        if (oldEntry == null) {
            size.addAndGet(key.length() + value.length + 16); // 16 bytes overhead
        } else {
            size.addAndGet(value.length - oldEntry.getValue().length);
        }
    }
    
    public byte[] get(String key) {
        Entry entry = data.get(key);
        if (entry != null && !entry.isDeleted()) {
            return entry.getValue();
        }
        return null;
    }
    
    public void delete(String key, long timestamp) {
        Entry tombstone = new Entry(null, timestamp, true);
        data.put(key, tombstone);
    }
    
    public long size() {
        return size.get();
    }
    
    public boolean isEmpty() {
        return data.isEmpty();
    }
    
    public void clear() {
        data.clear();
        size.set(0);
    }
    
    public Set<String> getKeys() {
        return data.keySet();
    }
    
    public Collection<Map.Entry<String, Entry>> getEntries() {
        return data.entrySet();
    }
}
```

### 3. Replication and Consistency

```java
@Service
public class ReplicationService {
    
    private final ConsistentHashRing hashRing;
    private final NodeCommunicationService nodeService;
    private final ExecutorService replicationExecutor;
    
    public WriteResult write(String key, byte[] value, ConsistencyLevel consistency) {
        List<Node> replicas = hashRing.getPreferenceList(key);
        
        switch (consistency) {
            case ONE:
                return writeToOne(key, value, replicas);
            case QUORUM:
                return writeToQuorum(key, value, replicas);
            case ALL:
                return writeToAll(key, value, replicas);
            default:
                throw new IllegalArgumentException("Unknown consistency level: " + consistency);
        }
    }
    
    public ReadResult read(String key, ConsistencyLevel consistency) {
        List<Node> replicas = hashRing.getPreferenceList(key);
        
        switch (consistency) {
            case ONE:
                return readFromOne(key, replicas);
            case QUORUM:
                return readFromQuorum(key, replicas);
            case ALL:
                return readFromAll(key, replicas);
            default:
                throw new IllegalArgumentException("Unknown consistency level: " + consistency);
        }
    }
    
    private WriteResult writeToQuorum(String key, byte[] value, List<Node> replicas) {
        int requiredAcks = (replicas.size() / 2) + 1;
        List<CompletableFuture<WriteResponse>> futures = new ArrayList<>();
        
        for (Node replica : replicas) {
            CompletableFuture<WriteResponse> future = CompletableFuture
                .supplyAsync(() -> nodeService.write(replica, key, value), replicationExecutor);
            futures.add(future);
        }
        
        int successCount = 0;
        List<Exception> failures = new ArrayList<>();
        
        for (CompletableFuture<WriteResponse> future : futures) {
            try {
                WriteResponse response = future.get(WRITE_TIMEOUT_MS, TimeUnit.MILLISECONDS);
                if (response.isSuccess()) {
                    successCount++;
                    if (successCount >= requiredAcks) {
                        return WriteResult.success();
                    }
                }
            } catch (Exception e) {
                failures.add(e);
            }
        }
        
        return WriteResult.failure("Failed to achieve quorum", failures);
    }
    
    private ReadResult readFromQuorum(String key, List<Node> replicas) {
        int requiredResponses = (replicas.size() / 2) + 1;
        List<CompletableFuture<ReadResponse>> futures = new ArrayList<>();
        
        for (Node replica : replicas) {
            CompletableFuture<ReadResponse> future = CompletableFuture
                .supplyAsync(() -> nodeService.read(replica, key), replicationExecutor);
            futures.add(future);
        }
        
        List<ReadResponse> responses = new ArrayList<>();
        
        for (CompletableFuture<ReadResponse> future : futures) {
            try {
                ReadResponse response = future.get(READ_TIMEOUT_MS, TimeUnit.MILLISECONDS);
                responses.add(response);
                
                if (responses.size() >= requiredResponses) {
                    break;
                }
            } catch (Exception e) {
                // Continue with other replicas
            }
        }
        
        if (responses.size() < requiredResponses) {
            return ReadResult.failure("Failed to achieve quorum");
        }
        
        // Resolve conflicts using timestamp (last-write-wins)
        ReadResponse latest = responses.stream()
            .filter(r -> r.getValue() != null)
            .max(Comparator.comparing(ReadResponse::getTimestamp))
            .orElse(null);
        
        if (latest != null) {
            // Trigger read repair if responses differ
            if (responsesConflict(responses)) {
                triggerReadRepair(key, latest, replicas);
            }
            
            return ReadResult.success(latest.getValue());
        }
        
        return ReadResult.notFound();
    }
    
    private void triggerReadRepair(String key, ReadResponse correctValue, List<Node> replicas) {
        // Asynchronously repair inconsistent replicas
        CompletableFuture.runAsync(() -> {
            for (Node replica : replicas) {
                try {
                    ReadResponse response = nodeService.read(replica, key);
                    if (response.getTimestamp() < correctValue.getTimestamp()) {
                        nodeService.write(replica, key, correctValue.getValue());
                    }
                } catch (Exception e) {
                    // Log error but don't fail read repair
                }
            }
        }, replicationExecutor);
    }
}
```

This distributed key-value store design provides a highly available, scalable storage system with tunable consistency and automatic failure handling.
