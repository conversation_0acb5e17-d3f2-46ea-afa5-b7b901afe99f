# System Design Interview Prep

This collection contains comprehensive system design explanations for the most commonly asked system design problems in technical interviews. Each design includes detailed architecture diagrams, thorough explanations, and real-world considerations.

## 📁 Directory Structure

```
src/design/
├── url-shortener/          # URL Shortener (like bit.ly, tinyurl) ✅
├── chat-system/            # Real-time Chat System (like WhatsApp, Slack) ✅
├── social-media-feed/      # Social Media News Feed (like Twitter, Facebook) ✅
├── video-streaming/        # Video Streaming Platform (like YouTube, Netflix) ✅
├── ride-sharing/           # Ride Sharing Service (like Uber, Lyft) ✅
├── search-engine/          # Web Search Engine (like Google Search) ✅
├── notification-system/    # Push Notification System ✅
├── distributed-cache/      # Distributed Cache System (like Redis Cluster) ✅
├── rate-limiter/          # API Rate Limiter ✅
├── web-crawler/           # Web Crawler System ✅
├── key-value-store/       # Distributed Key-Value Store ✅
└── payment-system/        # Payment Processing System ✅
```

## 🎯 System Design Categories

### **🔗 Core Web Services (4 designs)**
- **URL Shortener** - Scalable link shortening service with Base62 encoding
- **Web Crawler** - Large-scale web crawling with politeness and duplicate detection
- **Search Engine** - Distributed search with PageRank and inverted indexing
- **Rate Limiter** - API throttling with multiple algorithms (Token Bucket, Sliding Window)

### **💬 Real-time Systems (2 designs)**
- **Chat System** - Real-time messaging with WebSockets, Kafka, and Redis
- **Notification System** - Multi-channel push notifications at scale

### **📱 Social & Media Platforms (3 designs)**
- **Social Media Feed** - Timeline generation with ML ranking and caching
- **Video Streaming** - Video transcoding, CDN delivery, and adaptive streaming
- **Ride Sharing** - Real-time location tracking with geospatial matching

### **🏗️ Infrastructure & Storage (3 designs)**
- **Distributed Cache** - High-performance caching with consistent hashing
- **Key-Value Store** - Distributed NoSQL with LSM trees and tunable consistency
- **Payment System** - Secure PCI-compliant transaction processing

## 📊 Difficulty Levels

### 🟢 **Beginner (3 designs)** - Foundation concepts
- **URL Shortener** - Basic scalability, caching, database design
- **Rate Limiter** - Algorithms, distributed systems basics
- **Distributed Cache** - Consistent hashing, replication

### 🟡 **Intermediate (6 designs)** - Production systems
- **Chat System** - Real-time communication, WebSockets, message queues
- **Social Media Feed** - Complex algorithms, caching strategies, ML ranking
- **Notification System** - Multi-channel delivery, template engines, analytics
- **Web Crawler** - Large-scale data processing, politeness, duplicate detection
- **Key-Value Store** - Distributed storage, consistency models, LSM trees
- **Payment System** - Security, compliance, financial transactions

### 🔴 **Advanced (3 designs)** - Complex distributed systems
- **Video Streaming** - Content delivery, transcoding, global scale
- **Ride Sharing** - Real-time geospatial systems, dynamic pricing, matching
- **Search Engine** - Information retrieval, distributed computing, ranking algorithms

## 🛠️ Technologies & Concepts Covered

### **Databases**
- SQL vs NoSQL trade-offs
- Database sharding and partitioning
- Replication strategies
- Consistency models (ACID, BASE, CAP theorem)

### **Caching**
- Cache-aside, Write-through, Write-behind patterns
- Distributed caching (Redis, Memcached)
- CDN strategies
- Cache invalidation

### **Messaging & Communication**
- Message queues (Kafka, RabbitMQ)
- WebSockets for real-time communication
- gRPC vs REST APIs
- Event-driven architecture

### **Scalability Patterns**
- Load balancing strategies
- Horizontal vs vertical scaling
- Microservices architecture
- Database federation

### **Reliability & Performance**
- Circuit breaker pattern
- Bulkhead pattern
- Monitoring and alerting
- Disaster recovery

## 📈 Interview Preparation Guide

### **Phase 1: Foundation (Weeks 1-2)**
1. Study URL Shortener and Rate Limiter
2. Understand basic scalability concepts
3. Learn database fundamentals (SQL vs NoSQL)
4. Practice drawing simple architecture diagrams

### **Phase 2: Core Systems (Weeks 3-4)**
1. Deep dive into Chat System and Social Media Feed
2. Master caching strategies and message queues
3. Understand real-time system challenges
4. Practice capacity estimation

### **Phase 3: Advanced Topics (Weeks 5-6)**
1. Study Video Streaming and Search Engine
2. Learn about distributed systems concepts
3. Understand consistency and availability trade-offs
4. Practice complex system interactions

## 🎯 Interview Tips

### **System Design Interview Process**
1. **Clarify Requirements** (5-10 minutes)
   - Functional requirements
   - Non-functional requirements (scale, performance)
   - Constraints and assumptions

2. **High-Level Design** (10-15 minutes)
   - Draw major components
   - Show data flow
   - Identify key services

3. **Detailed Design** (15-20 minutes)
   - Deep dive into critical components
   - Database schema design
   - API design

4. **Scale & Optimize** (10-15 minutes)
   - Identify bottlenecks
   - Discuss scaling strategies
   - Address reliability concerns

### **Common Evaluation Criteria**
- **Problem Understanding** - Did you ask the right questions?
- **System Architecture** - Is the design scalable and maintainable?
- **Trade-off Analysis** - Can you justify design decisions?
- **Scalability** - How does the system handle growth?
- **Reliability** - What happens when things fail?

## 📚 Recommended Study Order

1. **Start with URL Shortener** - Covers all basic concepts
2. **Move to Chat System** - Introduces real-time challenges
3. **Study Social Media Feed** - Complex data modeling
4. **Explore Video Streaming** - Content delivery networks
5. **Master Search Engine** - Distributed computing at scale

Each design builds upon concepts from previous ones, creating a comprehensive learning path.

## 🎯 **What Makes This Collection Special**

### **📐 Diagram-as-Code Approach**
- **Mermaid diagrams** for every system architecture
- **Interactive visualizations** that can be rendered and modified
- **Version-controlled diagrams** that evolve with the design
- **Consistent visual language** across all system designs

### **💻 Production-Ready Code Examples**
- **Complete Java implementations** with proper error handling
- **Real-world patterns** used in production systems
- **Multiple solution approaches** for each component
- **Scalability considerations** built into every design

### **🏗️ Comprehensive Coverage**
- **End-to-end system design** from requirements to monitoring
- **Trade-off analysis** explaining design decisions
- **Capacity estimation** with realistic numbers
- **Failure scenarios** and recovery strategies

### **📚 Interview-Focused Structure**
- **Standard interview format** (Requirements → Architecture → Deep Dive → Scale)
- **Common follow-up questions** addressed in each design
- **Scalability discussions** for handling growth
- **Real company examples** and case studies

---

**Total: 12 comprehensive system designs covering all major interview topics**

Perfect preparation for system design interviews at FAANG and top tech companies! 🚀

## 🔗 **Quick Navigation**

| System | Difficulty | Key Concepts | Interview Frequency |
|--------|------------|--------------|-------------------|
| [URL Shortener](./url-shortener/) | 🟢 Beginner | Base62, Caching, Sharding | ⭐⭐⭐⭐⭐ |
| [Rate Limiter](./rate-limiter/) | 🟢 Beginner | Token Bucket, Sliding Window | ⭐⭐⭐⭐⭐ |
| [Distributed Cache](./distributed-cache/) | 🟢 Beginner | Consistent Hashing, LRU | ⭐⭐⭐⭐ |
| [Chat System](./chat-system/) | 🟡 Intermediate | WebSockets, Message Queues | ⭐⭐⭐⭐⭐ |
| [Social Media Feed](./social-media-feed/) | 🟡 Intermediate | Timeline, Ranking, Caching | ⭐⭐⭐⭐⭐ |
| [Notification System](./notification-system/) | 🟡 Intermediate | Multi-channel, Templates | ⭐⭐⭐⭐ |
| [Web Crawler](./web-crawler/) | 🟡 Intermediate | Distributed Processing | ⭐⭐⭐ |
| [Key-Value Store](./key-value-store/) | 🟡 Intermediate | LSM Trees, Consistency | ⭐⭐⭐⭐ |
| [Payment System](./payment-system/) | 🟡 Intermediate | Security, Compliance | ⭐⭐⭐ |
| [Video Streaming](./video-streaming/) | 🔴 Advanced | CDN, Transcoding | ⭐⭐⭐⭐ |
| [Ride Sharing](./ride-sharing/) | 🔴 Advanced | Geospatial, Real-time | ⭐⭐⭐⭐ |
| [Search Engine](./search-engine/) | 🔴 Advanced | Indexing, PageRank | ⭐⭐⭐ |
