# Real-time Chat System Design

Design a real-time chat system like WhatsApp, Slack, or Facebook Messenger that supports one-on-one and group messaging.

## 📋 Requirements

### Functional Requirements
1. **One-on-one Chat**: Send and receive messages between two users
2. **Group Chat**: Support group conversations (up to 500 members)
3. **Online Status**: Show user online/offline status
4. **Message History**: Store and retrieve chat history
5. **Message Delivery**: Delivery and read receipts
6. **Media Sharing**: Support images, videos, files
7. **Push Notifications**: Notify users of new messages

### Non-Functional Requirements
1. **Scale**: 50M daily active users, 1B messages per day
2. **Real-time**: < 100ms message delivery latency
3. **Availability**: 99.9% uptime
4. **Consistency**: Messages delivered in order
5. **Storage**: Message history for 1 year

## 🔢 Capacity Estimation

### Traffic Estimates
- **Daily messages**: 1B messages/day = ~11,600 messages/second
- **Peak traffic**: 5x average = ~58,000 messages/second
- **Concurrent connections**: 10M active users
- **Message size**: ~100 bytes average

### Storage Estimates
- **Messages per year**: 1B × 365 = 365B messages
- **Storage per message**: ~150 bytes (message + metadata)
- **Total storage**: 365B × 150 bytes = ~55TB per year
- **Media storage**: Additional 200TB per year

### Bandwidth Estimates
- **Incoming**: 11,600 × 100 bytes = ~1.16 MB/second
- **Outgoing**: 11,600 × 100 bytes × 2 (avg recipients) = ~2.32 MB/second

## 🏗️ High-Level Architecture

```mermaid
graph TB
    subgraph "Client Applications"
        MobileApp[Mobile Apps<br/>iOS/Android]
        WebApp[Web Application<br/>React/Vue]
        DesktopApp[Desktop Apps<br/>Electron]
    end

    subgraph "Load Balancing & Gateway"
        GlobalLB[Global Load Balancer<br/>GeoDNS Routing]
        WSGateway[WebSocket Gateway<br/>Connection Management]
        APIGateway[API Gateway<br/>REST Endpoints]
    end

    subgraph "Real-time Communication Layer"
        WSServer1[WebSocket Server 1<br/>Connection Pool]
        WSServer2[WebSocket Server 2<br/>Connection Pool]
        WSServer3[WebSocket Server 3<br/>Connection Pool]
        ConnectionManager[Connection Manager<br/>User Session Tracking]
    end

    subgraph "Core Application Services"
        ChatService[Chat Service<br/>Message Processing]
        UserService[User Service<br/>Authentication & Profiles]
        GroupService[Group Service<br/>Group Management]
        NotificationService[Notification Service<br/>Push Notifications]
        MediaService[Media Service<br/>File Upload/Download]
        PresenceService[Presence Service<br/>Online Status]
    end

    subgraph "Message Processing Pipeline"
        MessageValidator[Message Validator<br/>Content Filtering]
        MessageRouter[Message Router<br/>Delivery Logic]
        MessagePersistence[Message Persistence<br/>Database Writer]
        MessageDelivery[Message Delivery<br/>Real-time Sender]
    end

    subgraph "Message Queue System"
        MessageQueue[Message Queue<br/>Apache Kafka]
        DeliveryQueue[Delivery Queue<br/>Per-User Topics]
        NotificationQueue[Notification Queue<br/>Push Events]
        AnalyticsQueue[Analytics Queue<br/>Usage Metrics]
    end

    subgraph "Storage Systems"
        MessageDB[(Message Database<br/>Cassandra/MongoDB)]
        UserDB[(User Database<br/>PostgreSQL)]
        GroupDB[(Group Database<br/>PostgreSQL)]
        MediaStorage[(Media Storage<br/>S3/MinIO)]
        SessionCache[Session Cache<br/>Redis Cluster]
        MessageCache[Message Cache<br/>Redis Cluster]
    end

    subgraph "External Services"
        PushService[Push Notification<br/>FCM/APNS]
        EmailService[Email Service<br/>SendGrid]
        CDN[Content Delivery Network<br/>Media Distribution]
        VirusScanner[Virus Scanner<br/>File Security]
    end

    subgraph "Analytics & Monitoring"
        MetricsCollector[Metrics Collector<br/>Prometheus]
        LogAggregator[Log Aggregator<br/>ELK Stack]
        AlertManager[Alert Manager<br/>PagerDuty]
    end

    %% Client connections
    MobileApp --> GlobalLB
    WebApp --> GlobalLB
    DesktopApp --> GlobalLB

    %% Load balancing
    GlobalLB --> WSGateway
    GlobalLB --> APIGateway

    %% WebSocket connections
    WSGateway --> WSServer1
    WSGateway --> WSServer2
    WSGateway --> WSServer3

    %% Connection management
    WSServer1 --> ConnectionManager
    WSServer2 --> ConnectionManager
    WSServer3 --> ConnectionManager

    %% Service connections
    APIGateway --> ChatService
    APIGateway --> UserService
    APIGateway --> GroupService
    APIGateway --> MediaService

    WSServer1 --> ChatService
    WSServer2 --> ChatService
    WSServer3 --> ChatService

    %% Message processing flow
    ChatService --> MessageValidator
    MessageValidator --> MessageRouter
    MessageRouter --> MessagePersistence
    MessageRouter --> MessageDelivery

    %% Queue connections
    MessageRouter --> MessageQueue
    MessageQueue --> DeliveryQueue
    MessageQueue --> NotificationQueue
    MessageQueue --> AnalyticsQueue

    %% Message delivery
    MessageDelivery --> WSServer1
    MessageDelivery --> WSServer2
    MessageDelivery --> WSServer3

    %% Storage connections
    MessagePersistence --> MessageDB
    UserService --> UserDB
    GroupService --> GroupDB
    MediaService --> MediaStorage

    %% Caching
    ChatService --> MessageCache
    UserService --> SessionCache
    ConnectionManager --> SessionCache
    PresenceService --> SessionCache

    %% External services
    NotificationService --> PushService
    NotificationService --> EmailService
    MediaService --> CDN
    MediaService --> VirusScanner

    %% Monitoring
    ChatService --> MetricsCollector
    WSServer1 --> LogAggregator
    WSServer2 --> LogAggregator
    WSServer3 --> LogAggregator
    MetricsCollector --> AlertManager

    %% Notification flow
    NotificationQueue --> NotificationService
    AnalyticsQueue --> MetricsCollector

    classDef client fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef gateway fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef realtime fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef service fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef processing fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef queue fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef storage fill:#e0f2f1,stroke:#00796b,stroke-width:2px
    classDef external fill:#fff8e1,stroke:#ffa000,stroke-width:2px
    classDef monitoring fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px

    class MobileApp,WebApp,DesktopApp client
    class GlobalLB,WSGateway,APIGateway gateway
    class WSServer1,WSServer2,WSServer3,ConnectionManager realtime
    class ChatService,UserService,GroupService,NotificationService,MediaService,PresenceService service
    class MessageValidator,MessageRouter,MessagePersistence,MessageDelivery processing
    class MessageQueue,DeliveryQueue,NotificationQueue,AnalyticsQueue queue
    class MessageDB,UserDB,GroupDB,MediaStorage,SessionCache,MessageCache storage
    class PushService,EmailService,CDN,VirusScanner external
    class MetricsCollector,LogAggregator,AlertManager monitoring
```

## 🔧 Detailed Design

### 1. WebSocket Connection Management

**Connection Handling:**
```javascript
// Client-side WebSocket connection
const ws = new WebSocket('wss://chat.example.com/ws');

ws.onopen = function() {
    // Send authentication token
    ws.send(JSON.stringify({
        type: 'auth',
        token: 'user_jwt_token'
    }));
};

ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    handleIncomingMessage(message);
};
```

**Server-side Connection Pool:**
```java
// Maintain active connections
Map<String, WebSocketSession> userConnections = new ConcurrentHashMap<>();

// Handle new connection
public void onConnect(WebSocketSession session, String userId) {
    userConnections.put(userId, session);
    updateUserStatus(userId, "online");
}

// Handle disconnection
public void onDisconnect(String userId) {
    userConnections.remove(userId);
    updateUserStatus(userId, "offline");
}
```

### 2. Message Flow Architecture

**Message Sending Process:**
1. Client sends message via WebSocket
2. Chat service validates and processes message
3. Message published to Kafka topic
4. Message stored in database
5. Message delivered to recipient(s)
6. Delivery receipt sent back to sender

### 3. Database Schema

**Messages Table:**
```sql
CREATE TABLE messages (
    message_id VARCHAR(36) PRIMARY KEY,
    chat_id VARCHAR(36) NOT NULL,
    sender_id VARCHAR(36) NOT NULL,
    content TEXT,
    message_type ENUM('text', 'image', 'video', 'file'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_chat_id_created (chat_id, created_at),
    INDEX idx_sender_id (sender_id)
);
```

**Chats Table:**
```sql
CREATE TABLE chats (
    chat_id VARCHAR(36) PRIMARY KEY,
    chat_type ENUM('direct', 'group'),
    chat_name VARCHAR(255),
    created_by VARCHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**Chat Participants Table:**
```sql
CREATE TABLE chat_participants (
    chat_id VARCHAR(36),
    user_id VARCHAR(36),
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_read_message_id VARCHAR(36),
    role ENUM('admin', 'member') DEFAULT 'member',
    PRIMARY KEY (chat_id, user_id),
    INDEX idx_user_id (user_id)
);
```

### 4. Message Delivery System

**Kafka Topic Structure:**
```
Topic: user-messages-{user_id}
Partitions: Based on user_id hash
Retention: 7 days (for retry mechanism)
```

**Message Format:**
```json
{
    "message_id": "msg_123456",
    "chat_id": "chat_789",
    "sender_id": "user_456",
    "recipient_ids": ["user_789", "user_012"],
    "content": "Hello, how are you?",
    "message_type": "text",
    "timestamp": "2024-01-15T10:30:00Z",
    "metadata": {
        "reply_to": "msg_123455",
        "mentions": ["user_789"]
    }
}
```

### 5. Real-time Features

**Online Status Management:**
```redis
# Redis data structure for online users
SET user:online:user_123 "true" EX 300  # 5 minute expiry
SADD chat:online:chat_456 user_123      # Add to chat's online users set
```

**Typing Indicators:**
```javascript
// Client sends typing indicator
ws.send(JSON.stringify({
    type: 'typing',
    chat_id: 'chat_456',
    is_typing: true
}));

// Server broadcasts to other participants
broadcastToChat(chatId, {
    type: 'user_typing',
    user_id: senderId,
    is_typing: true
}, excludeUserId);
```

## ⚡ Scalability Solutions

### 1. Database Sharding
```
Shard by chat_id:
- Shard 1: chat_id hash % 4 == 0
- Shard 2: chat_id hash % 4 == 1
- Shard 3: chat_id hash % 4 == 2
- Shard 4: chat_id hash % 4 == 3
```

### 2. WebSocket Server Scaling
- **Consistent Hashing**: Route users to specific WebSocket servers
- **Service Discovery**: Dynamic server registration and discovery
- **Connection Pooling**: Manage connections efficiently

### 3. Message Queue Partitioning
```
Kafka Partitioning Strategy:
- Partition by recipient_user_id
- Ensures message ordering per user
- Enables parallel processing
```

## 🛡️ Security & Privacy

### Authentication & Authorization
```java
// JWT token validation
public boolean validateToken(String token) {
    try {
        Claims claims = Jwts.parser()
            .setSigningKey(secretKey)
            .parseClaimsJws(token)
            .getBody();
        return !claims.getExpiration().before(new Date());
    } catch (JwtException e) {
        return false;
    }
}
```

### Message Encryption
- **End-to-end encryption** for sensitive conversations
- **TLS encryption** for data in transit
- **Encryption at rest** for stored messages

### Rate Limiting
```
Rate limits per user:
- 100 messages per minute
- 1000 messages per hour
- 10000 messages per day
```

## 📱 Push Notifications

**Notification Service Integration:**
```java
// Send push notification when user is offline
public void sendPushNotification(String userId, Message message) {
    if (!isUserOnline(userId)) {
        PushNotification notification = PushNotification.builder()
            .userId(userId)
            .title(message.getSenderName())
            .body(message.getContent())
            .data(Map.of("chat_id", message.getChatId()))
            .build();
        
        pushNotificationService.send(notification);
    }
}
```

## 📊 Monitoring & Analytics

### Key Metrics
- **Message throughput**: Messages per second
- **Connection count**: Active WebSocket connections
- **Message delivery latency**: End-to-end delivery time
- **Error rates**: Failed message deliveries
- **User engagement**: Messages per user per day

### Health Checks
```java
@GetMapping("/health")
public ResponseEntity<Map<String, String>> healthCheck() {
    Map<String, String> status = new HashMap<>();
    status.put("database", checkDatabaseHealth());
    status.put("kafka", checkKafkaHealth());
    status.put("redis", checkRedisHealth());
    status.put("websocket", checkWebSocketHealth());
    
    return ResponseEntity.ok(status);
}
```

## 🔄 Advanced Features

### Message Search
- **Elasticsearch** for full-text search
- **Indexing strategy** by chat_id and timestamp
- **Search filters** by user, date range, message type

### File Sharing
- **Pre-signed URLs** for secure file uploads
- **CDN distribution** for fast file delivery
- **Virus scanning** for uploaded files

### Message Reactions
```sql
CREATE TABLE message_reactions (
    message_id VARCHAR(36),
    user_id VARCHAR(36),
    reaction_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (message_id, user_id, reaction_type)
);
```

This chat system design provides a robust, scalable foundation for real-time messaging with support for millions of concurrent users.
