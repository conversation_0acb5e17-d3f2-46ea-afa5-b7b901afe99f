package augment.tries;

/**
 * Implement Trie (Prefix Tree) - LeetCode 208
 * A trie (pronounced as "try") or prefix tree is a tree data structure used to efficiently 
 * store and retrieve keys in a dataset of strings.
 */
public class ImplementTrie {
    
    class TrieNode {
        TrieNode[] children;
        boolean isEndOfWord;
        
        public TrieNode() {
            children = new TrieNode[26]; // for lowercase letters a-z
            isEndOfWord = false;
        }
    }
    
    private TrieNode root;
    
    public ImplementTrie() {
        root = new TrieNode();
    }
    
    public void insert(String word) {
        TrieNode current = root;
        
        for (char c : word.toCharArray()) {
            int index = c - 'a';
            if (current.children[index] == null) {
                current.children[index] = new TrieNode();
            }
            current = current.children[index];
        }
        
        current.isEndOfWord = true;
    }
    
    public boolean search(String word) {
        TrieNode current = root;
        
        for (char c : word.toCharArray()) {
            int index = c - 'a';
            if (current.children[index] == null) {
                return false;
            }
            current = current.children[index];
        }
        
        return current.isEndOfWord;
    }
    
    public boolean startsWith(String prefix) {
        TrieNode current = root;
        
        for (char c : prefix.toCharArray()) {
            int index = c - 'a';
            if (current.children[index] == null) {
                return false;
            }
            current = current.children[index];
        }
        
        return true;
    }
    
    public static void main(String[] args) {
        ImplementTrie trie = new ImplementTrie();
        
        trie.insert("apple");
        System.out.println("Search 'apple': " + trie.search("apple"));   // true
        System.out.println("Search 'app': " + trie.search("app"));       // false
        System.out.println("StartsWith 'app': " + trie.startsWith("app")); // true
        
        trie.insert("app");
        System.out.println("Search 'app': " + trie.search("app"));       // true
    }
}
