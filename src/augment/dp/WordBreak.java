package augment.dp;

import java.util.*;

/**
 * Word Break - LeetCode 139
 * Given a string s and a dictionary of strings wordDict, return true if s can be segmented 
 * into a space-separated sequence of one or more dictionary words.
 */
public class WordBreak {
    
    public boolean wordBreak(String s, List<String> wordDict) {
        Set<String> wordSet = new HashSet<>(wordDict);
        boolean[] dp = new boolean[s.length() + 1];
        dp[0] = true; // Empty string can always be segmented
        
        for (int i = 1; i <= s.length(); i++) {
            for (int j = 0; j < i; j++) {
                if (dp[j] && wordSet.contains(s.substring(j, i))) {
                    dp[i] = true;
                    break;
                }
            }
        }
        
        return dp[s.length()];
    }
    
    // Alternative approach with memoization
    public boolean wordBreakMemo(String s, List<String> wordDict) {
        Set<String> wordSet = new HashSet<>(wordDict);
        Boolean[] memo = new Boolean[s.length()];
        return wordBreakHelper(s, 0, wordSet, memo);
    }
    
    private boolean wordBreakHelper(String s, int start, Set<String> wordSet, Boolean[] memo) {
        if (start == s.length()) {
            return true;
        }
        
        if (memo[start] != null) {
            return memo[start];
        }
        
        for (int end = start + 1; end <= s.length(); end++) {
            String word = s.substring(start, end);
            if (wordSet.contains(word) && wordBreakHelper(s, end, wordSet, memo)) {
                memo[start] = true;
                return true;
            }
        }
        
        memo[start] = false;
        return false;
    }
    
    public static void main(String[] args) {
        WordBreak solution = new WordBreak();
        
        String s1 = "leetcode";
        List<String> wordDict1 = Arrays.asList("leet", "code");
        System.out.println("Can break '" + s1 + "': " + solution.wordBreak(s1, wordDict1)); // true
        
        String s2 = "applepenapple";
        List<String> wordDict2 = Arrays.asList("apple", "pen");
        System.out.println("Can break '" + s2 + "': " + solution.wordBreak(s2, wordDict2)); // true
        
        String s3 = "catsandog";
        List<String> wordDict3 = Arrays.asList("cats", "dog", "sand", "and", "cat");
        System.out.println("Can break '" + s3 + "': " + solution.wordBreak(s3, wordDict3)); // false
    }
}
