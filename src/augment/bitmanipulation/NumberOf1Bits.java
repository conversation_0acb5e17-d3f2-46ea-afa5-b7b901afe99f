package augment.bitmanipulation;

/**
 * Number of 1 Bits - LeetCode 191
 * Write a function that takes an unsigned integer and returns the number of '1' bits it has 
 * (also known as the Hamming weight).
 */
public class NumberOf1Bits {
    
    // Approach 1: Check each bit
    public int hammingWeight(int n) {
        int count = 0;
        for (int i = 0; i < 32; i++) {
            if ((n & (1 << i)) != 0) {
                count++;
            }
        }
        return count;
    }
    
    // Approach 2: Right shift and check LSB
    public int hammingWeightShift(int n) {
        int count = 0;
        while (n != 0) {
            count += n & 1;
            n >>>= 1; // Unsigned right shift
        }
        return count;
    }
    
    // Approach 3: <PERSON>'s algorithm (most efficient)
    public int hammingWeightOptimal(int n) {
        int count = 0;
        while (n != 0) {
            n &= (n - 1); // Removes the rightmost 1-bit
            count++;
        }
        return count;
    }
    
    // Approach 4: Using built-in function
    public int hammingWeightBuiltIn(int n) {
        return Integer.bitCount(n);
    }
    
    public static void main(String[] args) {
        NumberOf1Bits solution = new NumberOf1Bits();
        
        int n1 = 0b00000000000000000000000000001011; // 11 in binary
        System.out.println("Number of 1 bits in " + n1 + ": " + solution.hammingWeight(n1)); // 3
        
        int n2 = 0b00000000000000000000000010000000; // 128 in binary
        System.out.println("Number of 1 bits in " + n2 + ": " + solution.hammingWeight(n2)); // 1
        
        int n3 = -3; // 11111101 in binary (32-bit representation)
        System.out.println("Number of 1 bits in " + n3 + ": " + solution.hammingWeightOptimal(n3)); // 30
    }
}
