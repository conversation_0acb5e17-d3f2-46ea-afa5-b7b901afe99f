# Interview Prep - 50+ Most Common Coding Problems

This collection contains 50+ of the most frequently asked coding interview problems, organized by category. Each solution includes multiple approaches where applicable, detailed comments, and test cases.

## 📁 Directory Structure

```
src/augment/
├── arrays/           # Array and String Problems (15 exercises)
├── linkedlist/       # Linked List Problems (6 exercises)
├── trees/            # Tree Problems (9 exercises)
├── dp/               # Dynamic Programming Problems (4 exercises)
├── search/           # Searching Problems (3 exercises)
├── strings/          # String Problems (2 exercises)
├── graphs/           # Graph Problems (2 exercises)
├── stacks/           # Stack Problems (2 exercises)
├── heaps/            # Heap Problems (1 exercise)
├── backtracking/     # Backtracking Problems (2 exercises)
├── intervals/        # Interval Problems (2 exercises)
├── design/           # System Design Problems (1 exercise)
├── math/             # Math Problems (1 exercise)
├── twopointers/      # Two Pointers Problems (1 exercise)
├── tries/            # Trie Problems (1 exercise)
├── bitmanipulation/  # Bit Manipulation Problems (2 exercises)
└── greedy/           # Greedy Problems (1 exercise)
```

## 🔢 Array and String Problems (15 exercises)

1. **ContainsDuplicate.java** - Check if array contains duplicates **(Easy)**
2. **ValidAnagram.java** - Check if two strings are anagrams **(Easy)**
3. **GroupAnagrams.java** - Group strings that are anagrams **(Medium)**
4. **TopKFrequentElements.java** - Find k most frequent elements **(Medium)**
5. **ProductExceptSelf.java** - Product of array except self **(Medium)**
6. **ValidPalindrome.java** - Check if string is a palindrome **(Easy)**
7. **ThreeSum.java** - Find triplets that sum to zero **(Medium)**
8. **ContainerWithMostWater.java** - Two pointer technique **(Medium)**
9. **BestTimeToBuyStock.java** - Maximum profit from stock prices **(Easy)**
10. **LongestSubstringWithoutRepeating.java** - Sliding window technique **(Medium)**
11. **MergeIntervals.java** - Merge overlapping intervals **(Medium)**
12. **SpiralMatrix.java** - Traverse matrix in spiral order **(Medium)**
13. **RotateArray.java** - Rotate array by k positions **(Medium)**
14. **MaxSubarray.java** - Kadane's algorithm for maximum subarray **(Medium)**
15. **SlidingWindowMaximum.java** - Sliding window maximum using deque **(Hard)**

## 🔗 Linked List Problems (6 exercises)

1. **ListNode.java** - Linked list node definition and utilities **(Utility)**
2. **ReverseLinkedList.java** - Reverse a linked list **(Easy)**
3. **LinkedListCycle.java** - Detect cycle in linked list **(Easy)**
4. **MergeTwoSortedLists.java** - Merge two sorted linked lists **(Easy)**
5. **RemoveNthFromEnd.java** - Remove nth node from end **(Medium)**
6. **ReorderList.java** - Reorder list in specific pattern **(Medium)**

## 🌳 Tree Problems (9 exercises)

1. **TreeNode.java** - Binary tree node definition **(Utility)**
2. **InvertBinaryTree.java** - Invert/flip a binary tree **(Easy)**
3. **MaxDepthBinaryTree.java** - Find maximum depth of tree **(Easy)**
4. **SameTree.java** - Check if two trees are identical **(Easy)**
5. **SubtreeOfAnotherTree.java** - Check if tree is subtree of another **(Easy)**
6. **LowestCommonAncestor.java** - LCA in binary search tree **(Medium)**
7. **BinaryTreeLevelOrder.java** - Level order traversal (BFS) **(Medium)**
8. **ValidateBST.java** - Validate binary search tree **(Medium)**
9. **SerializeDeserializeBinaryTree.java** - Serialize/deserialize tree **(Hard)**

## 🔄 Dynamic Programming Problems (4 exercises)

1. **ClimbingStairs.java** - Classic DP problem with Fibonacci pattern **(Easy)**
2. **CoinChange.java** - Minimum coins to make amount **(Medium)**
3. **LongestIncreasingSubsequence.java** - LIS with DP and binary search **(Medium)**
4. **WordBreak.java** - Check if string can be segmented **(Medium)**

## 🔍 Search Problems (3 exercises)

1. **BinarySearch.java** - Classic binary search implementation **(Easy)**
2. **SearchInRotatedArray.java** - Search in rotated sorted array **(Medium)**
3. **FindMinInRotatedArray.java** - Find minimum in rotated array **(Medium)**

## 📝 String Problems (2 exercises)

1. **LongestPalindromicSubstring.java** - Find longest palindromic substring **(Medium)**
2. **LongestCommonSubsequence.java** - Classic DP string problem **(Medium)**

## 🌐 Graph Problems (2 exercises)

1. **NumberOfIslands.java** - Count islands using DFS **(Medium)**
2. **CourseSchedule.java** - Topological sort and cycle detection **(Medium)**

## 📚 Stack Problems (2 exercises)

1. **ValidParentheses.java** - Check if parentheses are valid **(Easy)**
2. **MinStack.java** - Stack with constant time minimum retrieval **(Medium)**

## 🏔️ Heap Problems (1 exercise)

1. **KthLargestElement.java** - Find kth largest element using heap/quickselect **(Medium)**

## 🔄 Backtracking Problems (2 exercises)

1. **Permutations.java** - Generate all permutations of array **(Medium)**
2. **Combinations.java** - Generate all combinations of k elements **(Medium)**

## ⏰ Interval Problems (2 exercises)

1. **MeetingRooms.java** - Check if person can attend all meetings **(Easy)**
2. **MeetingRoomsII.java** - Minimum meeting rooms required **(Medium)**

## 🏗️ System Design Problems (1 exercise)

1. **LRUCache.java** - Least Recently Used cache implementation **(Medium)**

## 🔢 Math Problems (1 exercise)

1. **HappyNumber.java** - Determine if number is happy using cycle detection **(Easy)**

## 👆 Two Pointers Problems (1 exercise)

1. **TrappingRainWater.java** - Advanced two pointers with water trapping **(Hard)**

## 🌲 Trie Problems (1 exercise)

1. **ImplementTrie.java** - Prefix tree implementation **(Medium)**

## 🔢 Bit Manipulation Problems (2 exercises)

1. **SingleNumber.java** - Find single number using XOR **(Easy)**
2. **NumberOf1Bits.java** - Count 1 bits (Hamming weight) **(Easy)**

## 🎯 Greedy Problems (1 exercise)

1. **JumpGame.java** - Greedy algorithm for jump game **(Medium)**

## 🚀 How to Run

Each file contains a `main` method with test cases. To run any solution:

```bash
# Compile and run from the project root
javac src/augment/arrays/ContainsDuplicate.java
java -cp src augment.arrays.ContainsDuplicate
```

## 📊 Difficulty Breakdown

### 🟢 **Easy (16 exercises)** - Great for beginners and warm-up
- ContainsDuplicate, ValidAnagram, ValidPalindrome, BestTimeToBuyStock
- ReverseLinkedList, LinkedListCycle, MergeTwoSortedLists
- InvertBinaryTree, MaxDepthBinaryTree, SameTree, SubtreeOfAnotherTree
- ClimbingStairs, BinarySearch, ValidParentheses, MeetingRooms
- HappyNumber, SingleNumber, NumberOf1Bits

### 🟡 **Medium (31 exercises)** - Core interview level
- GroupAnagrams, TopKFrequentElements, ProductExceptSelf, ThreeSum, ContainerWithMostWater
- LongestSubstringWithoutRepeating, MergeIntervals, SpiralMatrix, RotateArray, MaxSubarray
- RemoveNthFromEnd, ReorderList, LowestCommonAncestor, BinaryTreeLevelOrder, ValidateBST
- CoinChange, LongestIncreasingSubsequence, WordBreak, SearchInRotatedArray, FindMinInRotatedArray
- LongestPalindromicSubstring, LongestCommonSubsequence, NumberOfIslands, CourseSchedule
- MinStack, KthLargestElement, Permutations, Combinations, MeetingRoomsII, LRUCache
- ImplementTrie, JumpGame

### 🔴 **Hard (3 exercises)** - Advanced/senior positions
- SlidingWindowMaximum, SerializeDeserializeBinaryTree, TrappingRainWater

## 📈 Complexity Analysis

Each solution includes:
- Time complexity analysis
- Space complexity analysis
- Multiple approaches where applicable (brute force vs optimized)

## 🎯 Interview Tips

1. **Start with brute force** - Always explain the naive approach first
2. **Optimize step by step** - Show your thought process
3. **Consider edge cases** - Empty arrays, single elements, etc.
4. **Test your solution** - Walk through examples
5. **Discuss trade-offs** - Time vs space complexity

## 📚 Problem Sources

Problems are sourced from:
- LeetCode (most common interview questions)
- Top tech company interview patterns
- Algorithmic problem-solving fundamentals

## 🎯 **Study Plan Recommendations**

### 🚀 **Beginner Path** (Start here if new to coding interviews)
1. Start with **Easy** problems to build confidence
2. Focus on fundamental patterns: arrays, strings, basic trees
3. Master two pointers, sliding window, and hash maps

### 💪 **Intermediate Path** (Ready for most interviews)
1. Complete all **Easy** problems first
2. Tackle **Medium** problems by category
3. Focus on DP, backtracking, and graph algorithms

### 🏆 **Advanced Path** (Senior/Staff level positions)
1. Master all **Easy** and **Medium** problems
2. Solve **Hard** problems multiple ways
3. Focus on optimization and system design aspects

## 🆕 Additional High-Value Patterns Covered

- **Sliding Window Maximum** - Advanced sliding window with deque
- **Stack-based Problems** - Parentheses validation, min stack design
- **Heap Operations** - Kth largest with multiple approaches
- **Backtracking** - Permutations and combinations generation
- **Interval Scheduling** - Meeting rooms optimization problems
- **Cache Design** - LRU cache with doubly linked list + hashmap
- **Cycle Detection** - Floyd's algorithm in different contexts
- **Trapping Rain Water** - Advanced two pointers technique
- **Topological Sort** - Course scheduling and dependency resolution
- **Tree Serialization** - Converting trees to/from strings
- **Trie Data Structure** - Prefix tree for string operations
- **Bit Manipulation** - XOR tricks and bit counting
- **Greedy Algorithms** - Jump game optimization

---

**Total: 50+ exercises covering the most essential coding interview patterns**

Good luck with your interview preparation! 🍀
