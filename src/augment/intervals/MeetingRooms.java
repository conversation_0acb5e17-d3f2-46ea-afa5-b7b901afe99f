package augment.intervals;

import java.util.*;

/**
 * Meeting Rooms - LeetCode 252
 * Given an array of meeting time intervals where intervals[i] = [starti, endi], 
 * determine if a person could attend all meetings.
 */
public class MeetingRooms {
    
    public boolean canAttendMeetings(int[][] intervals) {
        if (intervals.length <= 1) return true;
        
        // Sort intervals by start time
        Arrays.sort(intervals, (a, b) -> Integer.compare(a[0], b[0]));
        
        for (int i = 1; i < intervals.length; i++) {
            if (intervals[i][0] < intervals[i - 1][1]) {
                return false; // Overlap found
            }
        }
        
        return true;
    }
    
    public static void main(String[] args) {
        MeetingRooms solution = new MeetingRooms();
        
        int[][] intervals1 = {{0, 30}, {5, 10}, {15, 20}};
        System.out.println("Can attend all meetings: " + solution.canAttendMeetings(intervals1)); // false
        
        int[][] intervals2 = {{7, 10}, {2, 4}};
        System.out.println("Can attend all meetings: " + solution.canAttendMeetings(intervals2)); // true
    }
}
