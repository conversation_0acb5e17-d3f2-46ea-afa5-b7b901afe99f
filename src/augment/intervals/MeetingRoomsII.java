package augment.intervals;

import java.util.*;

/**
 * Meeting Rooms II - LeetCode 253
 * Given an array of meeting time intervals intervals where intervals[i] = [starti, endi], 
 * return the minimum number of conference rooms required.
 */
public class MeetingRoomsII {
    
    public int minMeetingRooms(int[][] intervals) {
        if (intervals.length == 0) return 0;
        
        // Create separate arrays for start and end times
        int[] starts = new int[intervals.length];
        int[] ends = new int[intervals.length];
        
        for (int i = 0; i < intervals.length; i++) {
            starts[i] = intervals[i][0];
            ends[i] = intervals[i][1];
        }
        
        Arrays.sort(starts);
        Arrays.sort(ends);
        
        int rooms = 0;
        int endPointer = 0;
        
        for (int i = 0; i < starts.length; i++) {
            if (starts[i] >= ends[endPointer]) {
                endPointer++;
            } else {
                rooms++;
            }
        }
        
        return rooms;
    }
    
    // Alternative solution using priority queue
    public int minMeetingRoomsPQ(int[][] intervals) {
        if (intervals.length == 0) return 0;
        
        Arrays.sort(intervals, (a, b) -> Integer.compare(a[0], b[0]));
        
        PriorityQueue<Integer> minHeap = new PriorityQueue<>();
        
        for (int[] interval : intervals) {
            if (!minHeap.isEmpty() && minHeap.peek() <= interval[0]) {
                minHeap.poll();
            }
            minHeap.offer(interval[1]);
        }
        
        return minHeap.size();
    }
    
    public static void main(String[] args) {
        MeetingRoomsII solution = new MeetingRoomsII();
        
        int[][] intervals1 = {{0, 30}, {5, 10}, {15, 20}};
        System.out.println("Min meeting rooms: " + solution.minMeetingRooms(intervals1)); // 2
        
        int[][] intervals2 = {{7, 10}, {2, 4}};
        System.out.println("Min meeting rooms: " + solution.minMeetingRooms(intervals2)); // 1
    }
}
