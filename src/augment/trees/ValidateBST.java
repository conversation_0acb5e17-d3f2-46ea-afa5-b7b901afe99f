package augment.trees;

/**
 * Validate Binary Search Tree - LeetCode 98
 * Given the root of a binary tree, determine if it is a valid binary search tree (BST).
 */
public class ValidateBST {
    
    public boolean isValidBST(TreeNode root) {
        return validate(root, null, null);
    }
    
    private boolean validate(TreeNode node, Integer min, Integer max) {
        if (node == null) {
            return true;
        }
        
        if ((min != null && node.val <= min) || (max != null && node.val >= max)) {
            return false;
        }
        
        return validate(node.left, min, node.val) && validate(node.right, node.val, max);
    }
    
    public static void main(String[] args) {
        ValidateBST solution = new ValidateBST();
        
        // Valid BST
        TreeNode root1 = new TreeNode(2);
        root1.left = new TreeNode(1);
        root1.right = new TreeNode(3);
        System.out.println("Valid BST: " + solution.isValidBST(root1)); // true
        
        // Invalid BST
        TreeNode root2 = new TreeNode(5);
        root2.left = new TreeNode(1);
        root2.right = new TreeNode(4);
        root2.right.left = new TreeNode(3);
        root2.right.right = new TreeNode(6);
        System.out.println("Valid BST: " + solution.isValidBST(root2)); // false
    }
}
