package augment.trees;

/**
 * Maximum Depth of Binary Tree - LeetCode 104
 * Given the root of a binary tree, return its maximum depth.
 */
public class MaxDepthBinaryTree {
    
    public int maxDepth(TreeNode root) {
        if (root == null) {
            return 0;
        }
        
        int leftDepth = maxDepth(root.left);
        int rightDepth = maxDepth(root.right);
        
        return Math.max(leftDepth, rightDepth) + 1;
    }
    
    public static void main(String[] args) {
        MaxDepthBinaryTree solution = new MaxDepthBinaryTree();
        
        TreeNode root = TreeNode.createSampleTree();
        System.out.println("Max depth: " + solution.maxDepth(root)); // Expected: 3
    }
}
