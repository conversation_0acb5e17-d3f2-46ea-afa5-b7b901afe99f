package augment.strings;

/**
 * Longest Common Subsequence - LeetCode 1143
 * Given two strings text1 and text2, return the length of their longest common subsequence.
 * If there is no common subsequence, return 0.
 */
public class LongestCommonSubsequence {
    
    // 2D DP approach - O(m*n) time, O(m*n) space
    public int longestCommonSubsequence(String text1, String text2) {
        int m = text1.length(), n = text2.length();
        int[][] dp = new int[m + 1][n + 1];
        
        for (int i = 1; i <= m; i++) {
            for (int j = 1; j <= n; j++) {
                if (text1.charAt(i - 1) == text2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1] + 1;
                } else {
                    dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
                }
            }
        }
        
        return dp[m][n];
    }
    
    // Space optimized - O(m*n) time, O(min(m,n)) space
    public int longestCommonSubsequenceOptimized(String text1, String text2) {
        // Make text1 the shorter string for space optimization
        if (text1.length() > text2.length()) {
            return longestCommonSubsequenceOptimized(text2, text1);
        }
        
        int m = text1.length(), n = text2.length();
        int[] prev = new int[m + 1];
        int[] curr = new int[m + 1];
        
        for (int j = 1; j <= n; j++) {
            for (int i = 1; i <= m; i++) {
                if (text1.charAt(i - 1) == text2.charAt(j - 1)) {
                    curr[i] = prev[i - 1] + 1;
                } else {
                    curr[i] = Math.max(prev[i], curr[i - 1]);
                }
            }
            int[] temp = prev;
            prev = curr;
            curr = temp;
        }
        
        return prev[m];
    }
    
    // Recursive with memoization
    public int longestCommonSubsequenceMemo(String text1, String text2) {
        int[][] memo = new int[text1.length()][text2.length()];
        for (int i = 0; i < text1.length(); i++) {
            for (int j = 0; j < text2.length(); j++) {
                memo[i][j] = -1;
            }
        }
        return lcsHelper(text1, text2, 0, 0, memo);
    }
    
    private int lcsHelper(String text1, String text2, int i, int j, int[][] memo) {
        if (i == text1.length() || j == text2.length()) {
            return 0;
        }
        
        if (memo[i][j] != -1) {
            return memo[i][j];
        }
        
        if (text1.charAt(i) == text2.charAt(j)) {
            memo[i][j] = 1 + lcsHelper(text1, text2, i + 1, j + 1, memo);
        } else {
            memo[i][j] = Math.max(
                lcsHelper(text1, text2, i + 1, j, memo),
                lcsHelper(text1, text2, i, j + 1, memo)
            );
        }
        
        return memo[i][j];
    }
    
    public static void main(String[] args) {
        LongestCommonSubsequence solution = new LongestCommonSubsequence();
        
        String text1 = "abcde", text2 = "ace";
        System.out.println("LCS length: " + solution.longestCommonSubsequence(text1, text2)); // 3
        
        String text3 = "abc", text4 = "abc";
        System.out.println("LCS length: " + solution.longestCommonSubsequence(text3, text4)); // 3
        
        String text5 = "abc", text6 = "def";
        System.out.println("LCS length: " + solution.longestCommonSubsequence(text5, text6)); // 0
    }
}
