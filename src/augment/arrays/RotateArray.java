package augment.arrays;

import java.util.Arrays;

/**
 * Rotate Array - LeetCode 189
 * Given an array, rotate the array to the right by k steps, where k is non-negative.
 */
public class RotateArray {
    
    // Approach 1: Using extra space - O(n) time, O(n) space
    public void rotate(int[] nums, int k) {
        int n = nums.length;
        k = k % n; // Handle cases where k > n
        
        int[] temp = new int[n];
        
        // Copy elements to temp array in rotated positions
        for (int i = 0; i < n; i++) {
            temp[(i + k) % n] = nums[i];
        }
        
        // Copy back to original array
        System.arraycopy(temp, 0, nums, 0, n);
    }
    
    // Approach 2: Using reverse - O(n) time, O(1) space
    public void rotateOptimal(int[] nums, int k) {
        int n = nums.length;
        k = k % n;
        
        // Reverse entire array
        reverse(nums, 0, n - 1);
        // Reverse first k elements
        reverse(nums, 0, k - 1);
        // Reverse remaining elements
        reverse(nums, k, n - 1);
    }
    
    private void reverse(int[] nums, int start, int end) {
        while (start < end) {
            int temp = nums[start];
            nums[start] = nums[end];
            nums[end] = temp;
            start++;
            end--;
        }
    }
    
    public static void main(String[] args) {
        RotateArray solution = new RotateArray();
        
        int[] nums1 = {1, 2, 3, 4, 5, 6, 7};
        System.out.println("Original: " + Arrays.toString(nums1));
        solution.rotateOptimal(nums1, 3);
        System.out.println("Rotated by 3: " + Arrays.toString(nums1));
        // Expected: [5, 6, 7, 1, 2, 3, 4]
        
        int[] nums2 = {-1, -100, 3, 99};
        System.out.println("Original: " + Arrays.toString(nums2));
        solution.rotateOptimal(nums2, 2);
        System.out.println("Rotated by 2: " + Arrays.toString(nums2));
        // Expected: [3, 99, -1, -100]
    }
}
