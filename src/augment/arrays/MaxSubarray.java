package augment.arrays;

/**
 * Maximum Subarray - LeetCode 53 (<PERSON><PERSON><PERSON>'s Algorithm)
 * Given an integer array nums, find the contiguous subarray (containing at least one number) 
 * which has the largest sum and return its sum.
 */
public class MaxSubarray {
    
    public int maxSubArray(int[] nums) {
        int maxSoFar = nums[0];
        int maxEndingHere = nums[0];
        
        for (int i = 1; i < nums.length; i++) {
            maxEndingHere = Math.max(nums[i], maxEndingHere + nums[i]);
            maxSoFar = Math.max(maxSoFar, maxEndingHere);
        }
        
        return maxSoFar;
    }
    
    // Alternative implementation
    public int maxSubArrayAlternative(int[] nums) {
        int maxSum = Integer.MIN_VALUE;
        int currentSum = 0;
        
        for (int num : nums) {
            currentSum += num;
            maxSum = Math.max(maxSum, currentSum);
            
            if (currentSum < 0) {
                currentSum = 0;
            }
        }
        
        return maxSum;
    }
    
    public static void main(String[] args) {
        MaxSubarray solution = new MaxSubarray();
        
        int[] nums1 = {-2, 1, -3, 4, -1, 2, 1, -5, 4};
        System.out.println("Max subarray sum: " + solution.maxSubArray(nums1)); // 6
        
        int[] nums2 = {1};
        System.out.println("Max subarray sum: " + solution.maxSubArray(nums2)); // 1
        
        int[] nums3 = {5, 4, -1, 7, 8};
        System.out.println("Max subarray sum: " + solution.maxSubArray(nums3)); // 23
    }
}
