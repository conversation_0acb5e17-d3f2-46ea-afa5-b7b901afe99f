package augment.graphs;

import java.util.*;

/**
 * Course Schedule - LeetCode 207
 * There are a total of numCourses courses you have to take, labeled from 0 to numCourses - 1.
 * You are given an array prerequisites where prerequisites[i] = [ai, bi] indicates that 
 * you must take course bi first if you want to take course ai.
 * Return true if you can finish all courses. Otherwise, return false.
 */
public class CourseSchedule {
    
    // DFS approach with cycle detection
    public boolean canFinish(int numCourses, int[][] prerequisites) {
        List<List<Integer>> graph = new ArrayList<>();
        for (int i = 0; i < numCourses; i++) {
            graph.add(new ArrayList<>());
        }
        
        // Build adjacency list
        for (int[] prereq : prerequisites) {
            graph.get(prereq[1]).add(prereq[0]);
        }
        
        int[] visited = new int[numCourses]; // 0: unvisited, 1: visiting, 2: visited
        
        for (int i = 0; i < numCourses; i++) {
            if (visited[i] == 0) {
                if (hasCycle(graph, visited, i)) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    private boolean hasCycle(List<List<Integer>> graph, int[] visited, int course) {
        if (visited[course] == 1) return true;  // Cycle detected
        if (visited[course] == 2) return false; // Already processed
        
        visited[course] = 1; // Mark as visiting
        
        for (int neighbor : graph.get(course)) {
            if (hasCycle(graph, visited, neighbor)) {
                return true;
            }
        }
        
        visited[course] = 2; // Mark as visited
        return false;
    }
    
    // BFS approach using Kahn's algorithm (topological sort)
    public boolean canFinishBFS(int numCourses, int[][] prerequisites) {
        int[] indegree = new int[numCourses];
        List<List<Integer>> graph = new ArrayList<>();
        
        for (int i = 0; i < numCourses; i++) {
            graph.add(new ArrayList<>());
        }
        
        // Build graph and calculate indegrees
        for (int[] prereq : prerequisites) {
            graph.get(prereq[1]).add(prereq[0]);
            indegree[prereq[0]]++;
        }
        
        Queue<Integer> queue = new LinkedList<>();
        for (int i = 0; i < numCourses; i++) {
            if (indegree[i] == 0) {
                queue.offer(i);
            }
        }
        
        int processed = 0;
        while (!queue.isEmpty()) {
            int course = queue.poll();
            processed++;
            
            for (int neighbor : graph.get(course)) {
                indegree[neighbor]--;
                if (indegree[neighbor] == 0) {
                    queue.offer(neighbor);
                }
            }
        }
        
        return processed == numCourses;
    }
    
    public static void main(String[] args) {
        CourseSchedule solution = new CourseSchedule();
        
        int[][] prereq1 = {{1, 0}};
        System.out.println("Can finish courses: " + solution.canFinish(2, prereq1)); // true
        
        int[][] prereq2 = {{1, 0}, {0, 1}};
        System.out.println("Can finish courses: " + solution.canFinish(2, prereq2)); // false
    }
}
