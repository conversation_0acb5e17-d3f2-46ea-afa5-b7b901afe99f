package augment.linkedlist;

/**
 * Linked List Cycle - LeetCode 141
 * Given head, the head of a linked list, determine if the linked list has a cycle in it.
 */
public class LinkedListCycle {
    
    public boolean hasCycle(ListNode head) {
        if (head == null || head.next == null) {
            return false;
        }
        
        ListNode slow = head;
        ListNode fast = head.next;
        
        while (slow != fast) {
            if (fast == null || fast.next == null) {
                return false;
            }
            slow = slow.next;
            fast = fast.next.next;
        }
        
        return true;
    }
    
    public static void main(String[] args) {
        LinkedListCycle solution = new LinkedListCycle();
        
        // Test case 1: No cycle
        ListNode head1 = ListNode.createList(new int[]{3, 2, 0, -4});
        System.out.println("Has cycle: " + solution.hasCycle(head1)); // false
        
        // Test case 2: With cycle (manually create)
        ListNode head2 = new ListNode(3);
        head2.next = new ListNode(2);
        head2.next.next = new ListNode(0);
        head2.next.next.next = new ListNode(-4);
        head2.next.next.next.next = head2.next; // Create cycle
        System.out.println("Has cycle: " + solution.hasCycle(head2)); // true
    }
}
