package augment.linkedlist;

/**
 * Merge Two Sorted Lists - LeetCode 21
 * You are given the heads of two sorted linked lists list1 and list2.
 * Merge the two lists in a one sorted list.
 */
public class MergeTwoSortedLists {
    
    public ListNode mergeTwoLists(ListNode list1, ListNode list2) {
        ListNode dummy = new ListNode(0);
        ListNode current = dummy;
        
        while (list1 != null && list2 != null) {
            if (list1.val <= list2.val) {
                current.next = list1;
                list1 = list1.next;
            } else {
                current.next = list2;
                list2 = list2.next;
            }
            current = current.next;
        }
        
        // Append remaining nodes
        current.next = (list1 != null) ? list1 : list2;
        
        return dummy.next;
    }
    
    // Recursive solution
    public ListNode mergeTwoListsRecursive(ListNode list1, ListNode list2) {
        if (list1 == null) return list2;
        if (list2 == null) return list1;
        
        if (list1.val <= list2.val) {
            list1.next = mergeTwoListsRecursive(list1.next, list2);
            return list1;
        } else {
            list2.next = mergeTwoListsRecursive(list1, list2.next);
            return list2;
        }
    }
    
    public static void main(String[] args) {
        MergeTwoSortedLists solution = new MergeTwoSortedLists();
        
        ListNode list1 = ListNode.createList(new int[]{1, 2, 4});
        ListNode list2 = ListNode.createList(new int[]{1, 3, 4});
        
        System.out.print("List 1: ");
        ListNode.printList(list1);
        System.out.print("List 2: ");
        ListNode.printList(list2);
        
        ListNode merged = solution.mergeTwoLists(list1, list2);
        System.out.print("Merged: ");
        ListNode.printList(merged);
    }
}
