package algoexpert.hard;

/**
 * Water Area
 * 
 * You're given an array of non-negative integers where each non-zero integer represents the 
 * height of a pillar of width 1. Imagine water being poured over all of the pillars; write 
 * a function that returns the surface area of the water trapped between the pillars viewed 
 * from the front. Note that spilled water should be ignored.
 * 
 * For example, given the array [0, 8, 0, 0, 5, 0, 0, 10, 0, 0, 1, 1, 0, 3], the water 
 * trapped can be visualized as follows:
 *        |
 *        |
 *        |       |
 *        |   |   |
 *        |   |   |
 *        |   |   |
 *        |   |   |
 *        |   |   |
 *    |   |   |   |     | |   |
 * The surface area of the water is 48.
 */
public class WaterArea {
    
    // O(n) time | O(n) space
    public static int waterArea(int[] heights) {
        if (heights.length == 0) return 0;
        
        int[] leftMaxes = new int[heights.length];
        leftMaxes[0] = heights[0];
        for (int i = 1; i < heights.length; i++) {
            leftMaxes[i] = Math.max(leftMaxes[i - 1], heights[i]);
        }
        
        int[] rightMaxes = new int[heights.length];
        rightMaxes[heights.length - 1] = heights[heights.length - 1];
        for (int i = heights.length - 2; i >= 0; i--) {
            rightMaxes[i] = Math.max(rightMaxes[i + 1], heights[i]);
        }
        
        int totalWaterTrapped = 0;
        for (int i = 0; i < heights.length; i++) {
            int minHeight = Math.min(leftMaxes[i], rightMaxes[i]);
            if (minHeight > heights[i]) {
                totalWaterTrapped += minHeight - heights[i];
            }
        }
        
        return totalWaterTrapped;
    }
    
    // O(n) time | O(1) space - Two pointers approach
    public static int waterAreaOptimal(int[] heights) {
        if (heights.length == 0) return 0;
        
        int leftIdx = 0;
        int rightIdx = heights.length - 1;
        int leftMax = heights[leftIdx];
        int rightMax = heights[rightIdx];
        int surfaceArea = 0;
        
        while (leftIdx < rightIdx) {
            if (heights[leftIdx] < heights[rightIdx]) {
                leftIdx++;
                leftMax = Math.max(leftMax, heights[leftIdx]);
                surfaceArea += leftMax - heights[leftIdx];
            } else {
                rightIdx--;
                rightMax = Math.max(rightMax, heights[rightIdx]);
                surfaceArea += rightMax - heights[rightIdx];
            }
        }
        
        return surfaceArea;
    }
    
    public static void main(String[] args) {
        int[] heights = {0, 8, 0, 0, 5, 0, 0, 10, 0, 0, 1, 1, 0, 3};
        System.out.println(waterArea(heights)); // Expected: 48
        System.out.println(waterAreaOptimal(heights)); // Expected: 48
    }
}
