package algoexpert.hard;

import java.util.*;

/**
 * Largest Rectangle In Histogram
 * 
 * Write a function that takes in an array of positive integers representing the heights of 
 * adjacent buildings and returns the area of the largest rectangle that can be created by 
 * any number of adjacent buildings, including just one building. Note that all buildings 
 * have the same width of 1 unit.
 * 
 * For example, given buildings = [2, 1, 2], the largest rectangle that can be created has 
 * an area of 2 (the rectangle can be created by either the first or third building).
 */
public class LargestRectangleInHistogram {
    
    // O(n) time | O(n) space
    public static int largestRectangleInHistogram(int[] heights) {
        Stack<Integer> stack = new Stack<>();
        int maxArea = 0;
        
        for (int i = 0; i <= heights.length; i++) {
            int height = (i == heights.length) ? 0 : heights[i];
            
            while (!stack.isEmpty() && heights[stack.peek()] >= height) {
                int poppedHeight = heights[stack.pop()];
                int width = stack.isEmpty() ? i : i - stack.peek() - 1;
                maxArea = Math.max(maxArea, poppedHeight * width);
            }
            
            stack.push(i);
        }
        
        return maxArea;
    }
    
    public static void main(String[] args) {
        int[] heights = {2, 1, 5, 6, 2, 3};
        System.out.println(largestRectangleInHistogram(heights)); // Expected: 10
        
        int[] heights2 = {2, 4};
        System.out.println(largestRectangleInHistogram(heights2)); // Expected: 4
    }
}
