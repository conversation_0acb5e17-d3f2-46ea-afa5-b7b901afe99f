package algoexpert.easy;

import java.util.*;

/**
 * Branch Sums
 * 
 * Write a function that takes in a Binary Tree and returns a list of its branch sums ordered 
 * from leftmost branch sum to rightmost branch sum.
 * 
 * A branch sum is the sum of all values in a Binary Tree branch. A Binary Tree branch is a 
 * path of nodes in a tree that starts at the root node and ends at any leaf node.
 */
public class BranchSums {
    
    public static class BinaryTree {
        int value;
        BinaryTree left;
        BinaryTree right;

        BinaryTree(int value) {
            this.value = value;
            this.left = null;
            this.right = null;
        }
    }
    
    // O(n) time | O(n) space
    public static List<Integer> branchSums(BinaryTree root) {
        List<Integer> sums = new ArrayList<>();
        calculateBranchSums(root, 0, sums);
        return sums;
    }
    
    public static void calculateBranchSums(BinaryTree node, int runningSum, List<Integer> sums) {
        if (node == null) return;
        
        int newRunningSum = runningSum + node.value;
        
        if (node.left == null && node.right == null) {
            sums.add(newRunningSum);
            return;
        }
        
        calculateBranchSums(node.left, newRunningSum, sums);
        calculateBranchSums(node.right, newRunningSum, sums);
    }
    
    public static void main(String[] args) {
        BinaryTree root = new BinaryTree(1);
        root.left = new BinaryTree(2);
        root.right = new BinaryTree(3);
        root.left.left = new BinaryTree(4);
        root.left.right = new BinaryTree(5);
        root.right.left = new BinaryTree(6);
        root.right.right = new BinaryTree(7);
        root.left.left.left = new BinaryTree(8);
        root.left.left.right = new BinaryTree(9);
        root.left.right.left = new BinaryTree(10);
        
        List<Integer> result = branchSums(root);
        System.out.println(result); // Expected: [15, 16, 18, 10, 11]
    }
}
