package algoexpert.easy;

import java.util.Arrays;
import java.util.HashSet;

/**
 * Two Number Sum
 *
 * Write a function that takes in a non-empty array of distinct integers and an integer representing a target sum. If any
 * two numbers in the input array sum up to the target sum, the function should return them in an array, in any order. If
 * no two numbers sum up to the target sum, the function should return an empty array.
 *
 * Note that the target sum has to be obtained by summing two different integers in the array; you can't add a single
 * integer to itself in order to obtain the target sum.
 *
 * You can assume that there will be at most one pair of numbers summing up to the target sum.
 */
public class TwoNumberSum {
    public static void main(String[] args) {
        int[] result = twoNumberSum(new int[]{3, 5, -4, 8, 11, 1, -1, 6}, 10);
        for (int i : result) {
            System.out.println(i);
        }
    }

    // O(N^2) time and O(1) space
    public static int[] twoNumberSum(int[] array, int targetSum) {
        for (int i = 0; i < array.length; i++) {
            for (int j = i + 1; j < array.length; j++) {
                if (array[i] + array[j] == targetSum) {
                    return new int[]{array[i], array[j]};
                }
            }
        }
        return new int[0];
    }

    // O(N) time and O(N) space
    public static int[] twoNumberSum2(int[] array, int targetSum) {
        java.util.HashSet<Integer> nums = new java.util.HashSet<>();
        for (int num : array) {
            int potentialMatch = targetSum - num;
            if (nums.contains(potentialMatch)) {
                return new int[]{potentialMatch, num};
            } else {
                nums.add(num);
            }
        }
        return new int[0];
    }

    // O(NlogN) time and O(1) space
    public static int[] twoNumberSum3(int[] array, int targetSum) {
        java.util.Arrays.sort(array);
        int left = 0;
        int right = array.length - 1;
        while (left < right) {
            int currentSum = array[left] + array[right];
            if (currentSum == targetSum) {
                return new int[]{array[left], array[right]};
            } else if (currentSum < targetSum) {
                left++;
            } else {
                right--;
            }
        }
        return new int[0];
    }
}
