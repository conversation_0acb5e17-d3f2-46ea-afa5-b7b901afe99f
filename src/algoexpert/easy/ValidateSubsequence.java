package algoexpert.easy;

public class ValidateSubsequence {

    public static void main(String[] args) {
        int[] array = {5, 1, 22, 25, 6, -1, 8, 10};
        int[] sequence = {1, 6, -1, 10};
        System.out.println(isValidSubsequence(array, sequence));
    }

    // Time Complexity: O(n) | Space Complexity: O(1)
    public static boolean isValidSubsequence(int[] array, int[] sequence) {
        int sequenceIndex = 0;
        for (int i = 0; i < array.length; i++) {
            int currentValue = array[i];
            int sequenceValue = sequence[sequenceIndex];
            if (currentValue == sequenceValue) {
                sequenceIndex++;
            }
            // Prevents iterating through the entire array if the sequence is already found
            if (sequenceIndex == sequence.length) {
                break;
            }
        }
        return sequenceIndex == sequence.length;
    }

}
