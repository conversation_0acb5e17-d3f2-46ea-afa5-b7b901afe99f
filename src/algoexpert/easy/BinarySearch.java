package algoexpert.easy;

/**
 * Binary Search
 * 
 * Write a function that takes in a sorted array of integers as well as a target integer. 
 * The function should use the Binary Search algorithm to determine if the target integer 
 * is contained in the array and should return its index if it is, otherwise -1.
 */
public class BinarySearch {
    
    // O(log(n)) time | O(log(n)) space - Recursive approach
    public static int binarySearch(int[] array, int target) {
        return binarySearchHelper(array, target, 0, array.length - 1);
    }
    
    public static int binarySearchHelper(int[] array, int target, int left, int right) {
        if (left > right) {
            return -1;
        }
        int middle = (left + right) / 2;
        int potentialMatch = array[middle];
        if (target == potentialMatch) {
            return middle;
        } else if (target < potentialMatch) {
            return binarySearchHelper(array, target, left, middle - 1);
        } else {
            return binarySearchHelper(array, target, middle + 1, right);
        }
    }
    
    // O(log(n)) time | O(1) space - Iterative approach
    public static int binarySearchIterative(int[] array, int target) {
        int left = 0;
        int right = array.length - 1;
        while (left <= right) {
            int middle = (left + right) / 2;
            int potentialMatch = array[middle];
            if (target == potentialMatch) {
                return middle;
            } else if (target < potentialMatch) {
                right = middle - 1;
            } else {
                left = middle + 1;
            }
        }
        return -1;
    }
    
    public static void main(String[] args) {
        int[] array = {0, 1, 21, 33, 45, 45, 61, 71, 72, 73};
        System.out.println(binarySearch(array, 33)); // Expected: 3
        System.out.println(binarySearchIterative(array, 33)); // Expected: 3
        System.out.println(binarySearch(array, 51)); // Expected: -1
    }
}
