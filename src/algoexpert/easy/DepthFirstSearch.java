package algoexpert.easy;

import java.util.*;

/**
 * Depth-first Search
 * 
 * You're given a Node class that has a name and an array of optional children nodes. When put 
 * together, nodes form an acyclic tree-like structure.
 * 
 * Implement the depthFirstSearch method on the Node class, which takes in an empty array, 
 * traverses the tree using the depth-first search approach (specifically, the pre-order traversal 
 * variant), stores all of the nodes' names in the input array, and returns it.
 */
public class DepthFirstSearch {
    
    static class Node {
        String name;
        List<Node> children = new ArrayList<Node>();

        public Node(String name) {
            this.name = name;
        }

        // O(v + e) time | O(v) space
        public List<String> depthFirstSearch(List<String> array) {
            array.add(this.name);
            for (int i = 0; i < this.children.size(); i++) {
                this.children.get(i).depthFirstSearch(array);
            }
            return array;
        }

        public Node addChild(String name) {
            Node child = new Node(name);
            children.add(child);
            return this;
        }
    }
    
    public static void main(String[] args) {
        Node graph = new Node("A");
        graph.addChild("B").addChild("C").addChild("D");
        graph.children.get(0).addChild("E").addChild("F");
        graph.children.get(2).addChild("G").addChild("H");
        graph.children.get(0).children.get(1).addChild("I").addChild("J");
        graph.children.get(2).children.get(0).addChild("K");
        
        List<String> result = new ArrayList<>();
        graph.depthFirstSearch(result);
        System.out.println(result); // Expected: [A, B, E, F, I, J, C, D, G, K, H]
    }
}
