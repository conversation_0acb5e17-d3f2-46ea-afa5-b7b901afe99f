package algoexpert.easy;

/**
 * Nth Fibonacci
 * 
 * The Fibonacci sequence is defined as follows: the first number of the sequence is 0, the second 
 * number is 1, and the nth number is the sum of the (n - 1)th and (n - 2)th numbers. Write a 
 * function that takes in an integer n and returns the nth Fibonacci number.
 * 
 * Important note: the Fibonacci sequence is often defined with its first two numbers as F0 = 0 and 
 * F1 = 1. For the purpose of this question, the first Fibonacci number is F0; therefore, 
 * getNthFib(1) is equal to F0, getNthFib(2) is equal to F1, etc..
 */
public class NthFibonacci {
    
    // O(2^n) time | O(n) space - Naive recursive approach
    public static int getNthFib(int n) {
        if (n == 2) {
            return 1;
        } else if (n == 1) {
            return 0;
        } else {
            return getNthFib(n - 1) + getNthFib(n - 2);
        }
    }
    
    // O(n) time | O(n) space - Memoization approach
    public static int getNthFibMemo(int n) {
        java.util.Map<Integer, Integer> memoize = new java.util.HashMap<>();
        memoize.put(1, 0);
        memoize.put(2, 1);
        return getNthFibHelper(n, memoize);
    }
    
    public static int getNthFibHelper(int n, java.util.Map<Integer, Integer> memoize) {
        if (memoize.containsKey(n)) {
            return memoize.get(n);
        } else {
            memoize.put(n, getNthFibHelper(n - 1, memoize) + getNthFibHelper(n - 2, memoize));
            return memoize.get(n);
        }
    }
    
    // O(n) time | O(1) space - Iterative approach
    public static int getNthFibIterative(int n) {
        int[] lastTwo = {0, 1};
        int counter = 3;
        while (counter <= n) {
            int nextFib = lastTwo[0] + lastTwo[1];
            lastTwo[0] = lastTwo[1];
            lastTwo[1] = nextFib;
            counter++;
        }
        return n > 1 ? lastTwo[1] : lastTwo[0];
    }
    
    public static void main(String[] args) {
        System.out.println(getNthFib(6)); // Expected: 5
        System.out.println(getNthFibMemo(6)); // Expected: 5
        System.out.println(getNthFibIterative(6)); // Expected: 5
    }
}
