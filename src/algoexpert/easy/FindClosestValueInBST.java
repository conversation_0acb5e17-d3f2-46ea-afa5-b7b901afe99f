package algoexpert.easy;

/**
 * Find Closest Value In BST
 * 
 * Write a function that takes in a Binary Search Tree (BST) and a target integer value and returns 
 * the closest value to that target value contained in the BST.
 * 
 * You can assume that there will only be one closest value.
 */
public class FindClosestValueInBST {
    
    static class BST {
        public int value;
        public BST left;
        public BST right;

        public BST(int value) {
            this.value = value;
        }
    }
    
    // O(log(n)) time on average, O(n) worst case | O(1) space
    public static int findClosestValueInBst(BST tree, int target) {
        return findClosestValueInBstHelper(tree, target, tree.value);
    }
    
    public static int findClosestValueInBstHelper(BST tree, int target, int closest) {
        if (tree == null) return closest;
        
        if (Math.abs(target - closest) > Math.abs(target - tree.value)) {
            closest = tree.value;
        }
        
        if (target < tree.value) {
            return findClosestValueInBstHelper(tree.left, target, closest);
        } else if (target > tree.value) {
            return findClosestValueInBstHelper(tree.right, target, closest);
        } else {
            return closest;
        }
    }
    
    // Iterative approach - O(log(n)) time on average, O(n) worst case | O(1) space
    public static int findClosestValueInBstIterative(BST tree, int target) {
        int closest = tree.value;
        BST currentNode = tree;
        
        while (currentNode != null) {
            if (Math.abs(target - closest) > Math.abs(target - currentNode.value)) {
                closest = currentNode.value;
            }
            
            if (target < currentNode.value) {
                currentNode = currentNode.left;
            } else if (target > currentNode.value) {
                currentNode = currentNode.right;
            } else {
                break;
            }
        }
        
        return closest;
    }
    
    public static void main(String[] args) {
        BST root = new BST(10);
        root.left = new BST(5);
        root.right = new BST(15);
        root.left.left = new BST(2);
        root.left.right = new BST(5);
        root.right.left = new BST(13);
        root.right.right = new BST(22);
        root.left.left.left = new BST(1);
        root.right.left.right = new BST(14);
        
        System.out.println(findClosestValueInBst(root, 12)); // Expected: 13
    }
}
