package algoexpert.easy;

/**
 * Node Depths
 * 
 * The distance between a node in a Binary Tree and the tree's root is called the node's depth.
 * Write a function that takes in a Binary Tree and returns the sum of its nodes' depths.
 */
public class NodeDepths {
    
    static class BinaryTree {
        int value;
        BinaryTree left;
        BinaryTree right;

        public BinaryTree(int value) {
            this.value = value;
            this.left = null;
            this.right = null;
        }
    }
    
    // O(n) time | O(h) space where h is the height of the binary tree
    public static int nodeDepths(BinaryTree root) {
        return nodeDepthsHelper(root, 0);
    }
    
    public static int nodeDepthsHelper(BinaryTree node, int depth) {
        if (node == null) return 0;
        
        return depth + nodeDepthsHelper(node.left, depth + 1) + nodeDepths<PERSON>elper(node.right, depth + 1);
    }
    
    // Iterative approach using stack
    public static int nodeDepthsIterative(BinaryTree root) {
        int sumOfDepths = 0;
        java.util.Stack<NodeInfo> stack = new java.util.Stack<>();
        stack.push(new NodeInfo(root, 0));
        
        while (!stack.isEmpty()) {
            NodeInfo nodeInfo = stack.pop();
            BinaryTree node = nodeInfo.node;
            int depth = nodeInfo.depth;
            
            if (node == null) continue;
            
            sumOfDepths += depth;
            stack.push(new NodeInfo(node.left, depth + 1));
            stack.push(new NodeInfo(node.right, depth + 1));
        }
        
        return sumOfDepths;
    }
    
    static class NodeInfo {
        BinaryTree node;
        int depth;
        
        public NodeInfo(BinaryTree node, int depth) {
            this.node = node;
            this.depth = depth;
        }
    }
    
    public static void main(String[] args) {
        BinaryTree root = new BinaryTree(1);
        root.left = new BinaryTree(2);
        root.right = new BinaryTree(3);
        root.left.left = new BinaryTree(4);
        root.left.right = new BinaryTree(5);
        root.right.left = new BinaryTree(6);
        root.right.right = new BinaryTree(7);
        root.left.left.left = new BinaryTree(8);
        root.left.left.right = new BinaryTree(9);
        
        System.out.println(nodeDepths(root)); // Expected: 16
    }
}
