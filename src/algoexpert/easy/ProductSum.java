package algoexpert.easy;

import java.util.*;

/**
 * Product Sum
 * 
 * Write a function that takes in a "special" array and returns its product sum.
 * 
 * A "special" array is a non-empty array that contains either integers or other "special" arrays. 
 * The product sum of a "special" array is the sum of its elements, where "special" arrays inside 
 * it are summed themselves and then multiplied by their level of depth.
 * 
 * The depth of a "special" array is how far nested it is. For instance, the depth of [] is 1; 
 * the depth of the inner array in [[]] is 2; the depth of the innermost array in [[[]]] is 3.
 * 
 * You can assume that the "special" array passed to your function is never empty.
 */
public class ProductSum {
    
    // O(n) time | O(d) space where n is the total number of elements in the array,
    // including sub-elements, and d is the greatest depth of "special" arrays in the array
    public static int productSum(List<Object> array) {
        return productSumHelper(array, 1);
    }
    
    public static int productSumHelper(List<Object> array, int multiplier) {
        int sum = 0;
        for (Object el : array) {
            if (el instanceof ArrayList) {
                @SuppressWarnings("unchecked")
                ArrayList<Object> ls = (ArrayList<Object>) el;
                sum += productSumHelper(ls, multiplier + 1);
            } else {
                sum += (int) el;
            }
        }
        return sum * multiplier;
    }
    
    public static void main(String[] args) {
        List<Object> test = new ArrayList<Object>();
        test.add(5);
        test.add(2);
        
        List<Object> inner1 = new ArrayList<Object>();
        inner1.add(7);
        inner1.add(-1);
        test.add(inner1);
        
        test.add(3);
        
        List<Object> inner2 = new ArrayList<Object>();
        inner2.add(6);
        
        List<Object> inner3 = new ArrayList<Object>();
        inner3.add(-13);
        inner3.add(8);
        inner2.add(inner3);
        
        inner2.add(4);
        test.add(inner2);
        
        System.out.println(productSum(test)); // Expected: 12
        // Calculation: 5 + 2 + 2 * (7 - 1) + 3 + 2 * (6 + 3 * (-13 + 8) + 4)
        //            = 5 + 2 + 2 * 6 + 3 + 2 * (6 + 3 * (-5) + 4)
        //            = 5 + 2 + 12 + 3 + 2 * (6 - 15 + 4)
        //            = 5 + 2 + 12 + 3 + 2 * (-5)
        //            = 22 - 10 = 12
    }
}
