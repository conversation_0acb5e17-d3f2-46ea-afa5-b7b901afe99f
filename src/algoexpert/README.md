# AlgoExpert Problems Collection

This collection contains carefully selected problems from AlgoExpert, organized by difficulty level. Each solution includes multiple approaches where applicable, detailed complexity analysis, and comprehensive test cases.

## 📁 Directory Structure

```
src/algoexpert/
├── easy/         # Easy Problems (12 exercises)
├── medium/       # Medium Problems (8 exercises)
└── hard/         # Hard Problems (6 exercises)
```

## 🟢 Easy Problems (12 exercises)

1. **TwoNumberSum.java** - Find two numbers that sum to target **(Easy)**
2. **ValidateSubsequence.java** - Check if array is subsequence of another **(Easy)**
3. **FindClosestValueInBST.java** - Find closest value in binary search tree **(Easy)**
4. **BranchSums.java** - Calculate all branch sums in binary tree **(Easy)**
5. **NodeDepths.java** - Sum of all node depths in binary tree **(Easy)**
6. **DepthFirstSearch.java** - DFS traversal implementation **(Easy)**
7. **NthFibonacci.java** - Calculate nth Fibonacci number **(Easy)**
8. **ProductSum.java** - Calculate product sum of nested arrays **(Easy)**
9. **BinarySearch.java** - Classic binary search implementation **(Easy)**
10. **FindThreeLargestNumbers.java** - Find three largest numbers without sorting **(Easy)**
11. **BubbleSort.java** - Bubble sort algorithm implementation **(Easy)**

## 🟡 Medium Problems (8 exercises)

1. **ThreeNumberSum.java** - Find all triplets that sum to target **(Medium)**
2. **SmallestDifference.java** - Find pair with smallest absolute difference **(Medium)**
3. **MoveElementToEnd.java** - Move all instances of element to end **(Medium)**
4. **MonotonicArray.java** - Check if array is monotonic **(Medium)**
5. **SpiralTraverse.java** - Traverse 2D array in spiral order **(Medium)**
6. **LongestPeak.java** - Find longest peak in array **(Medium)**
7. **BreadthFirstSearch.java** - BFS traversal implementation **(Medium)**

## 🔴 Hard Problems (6 exercises)

1. **FourNumberSum.java** - Find all quadruplets that sum to target **(Hard)**
2. **SubarraySort.java** - Find smallest subarray that needs sorting **(Hard)**
3. **LargestRectangleInHistogram.java** - Largest rectangle area in histogram **(Hard)**
4. **MinNumberOfJumps.java** - Minimum jumps to reach end of array **(Hard)**
5. **WaterArea.java** - Water trapping problem (surface area) **(Hard)**

## 🎯 Key Algorithmic Patterns Covered

### **Array Manipulation**
- Two pointers technique (TwoNumberSum, SmallestDifference)
- Sliding window (LongestPeak)
- Array traversal patterns (SpiralTraverse, MoveElementToEnd)

### **Tree Algorithms**
- Binary Search Tree operations (FindClosestValueInBST)
- Tree traversal (BranchSums, NodeDepths)
- Depth-first and Breadth-first search

### **Dynamic Programming**
- Fibonacci sequence (multiple approaches)
- Jump game variations (MinNumberOfJumps)

### **Sorting Algorithms**
- Bubble Sort implementation
- Subarray sorting problems

### **Advanced Techniques**
- Stack-based solutions (LargestRectangleInHistogram)
- Hash map optimizations (FourNumberSum)
- Two pointers for optimization (WaterArea)

## 📊 Complexity Analysis

Each solution includes:
- **Time Complexity** - Big O notation for time
- **Space Complexity** - Big O notation for space
- **Multiple Approaches** - Often 2-3 different solutions per problem
- **Optimization Techniques** - From brute force to optimal solutions

## 🚀 How to Run

Each file contains a `main` method with test cases:

```bash
# Compile and run from the project root
javac src/algoexpert/easy/TwoNumberSum.java
java -cp src algoexpert.easy.TwoNumberSum
```

## 🎓 Study Recommendations

### **Beginner Path**
1. Start with **Easy** problems to understand fundamental patterns
2. Focus on: TwoNumberSum, BinarySearch, NthFibonacci
3. Master basic tree traversals: BranchSums, NodeDepths

### **Intermediate Path**
1. Complete all Easy problems first
2. Tackle Medium problems: ThreeNumberSum, SpiralTraverse
3. Focus on two-pointers and array manipulation techniques

### **Advanced Path**
1. Master Easy and Medium problems
2. Solve Hard problems: FourNumberSum, WaterArea
3. Focus on optimization and multiple solution approaches

## 🏆 AlgoExpert-Specific Features

- **Multiple Solutions**: Each problem shows progression from brute force to optimal
- **Detailed Comments**: Extensive explanation of approach and edge cases
- **Real Interview Format**: Problems formatted exactly as they appear on AlgoExpert
- **Complexity Analysis**: Every solution includes time/space complexity
- **Edge Case Handling**: Comprehensive coverage of corner cases

## 📈 Difficulty Distribution

- **Easy (46%)**: Foundation building, basic patterns
- **Medium (31%)**: Core interview level, advanced patterns  
- **Hard (23%)**: Complex algorithms, optimization challenges

This mirrors the actual AlgoExpert platform distribution and prepares you for real coding interviews at top tech companies.

---

**Total: 26 AlgoExpert problems covering essential coding interview patterns**

Perfect for systematic preparation following the AlgoExpert curriculum! 🚀
