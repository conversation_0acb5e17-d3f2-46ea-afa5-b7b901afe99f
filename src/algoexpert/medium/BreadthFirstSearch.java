package algoexpert.medium;

import java.util.*;

/**
 * Breadth-first Search
 * 
 * You're given a Node class that has a name and an array of optional children nodes. When put 
 * together, nodes form an acyclic tree-like structure.
 * 
 * Implement the breadthFirstSearch method on the Node class, which takes in an empty array, 
 * traverses the tree using the breadth-first search approach (specifically, the level-order 
 * traversal variant), stores all of the nodes' names in the input array, and returns it.
 */
public class BreadthFirstSearch {
    
    static class Node {
        String name;
        List<Node> children = new ArrayList<Node>();

        public Node(String name) {
            this.name = name;
        }

        // O(v + e) time | O(v) space
        public List<String> breadthFirstSearch(List<String> array) {
            Queue<Node> queue = new LinkedList<Node>();
            queue.add(this);
            while (!queue.isEmpty()) {
                Node current = queue.poll();
                array.add(current.name);
                queue.addAll(current.children);
            }
            return array;
        }

        public Node addChild(String name) {
            Node child = new Node(name);
            children.add(child);
            return this;
        }
    }
    
    public static void main(String[] args) {
        Node graph = new Node("A");
        graph.addChild("B").addChild("C").addChild("D");
        graph.children.get(0).addChild("E").addChild("F");
        graph.children.get(2).addChild("G").addChild("H");
        graph.children.get(0).children.get(1).addChild("I").addChild("J");
        graph.children.get(2).children.get(0).addChild("K");
        
        List<String> result = new ArrayList<>();
        graph.breadthFirstSearch(result);
        System.out.println(result); // Expected: [A, B, C, D, E, F, G, H, I, J, K]
    }
}
