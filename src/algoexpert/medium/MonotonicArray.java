package algoexpert.medium;

/**
 * Monotonic Array
 * 
 * Write a function that takes in an array of integers and returns a boolean representing 
 * whether the array is monotonic.
 * 
 * An array is said to be monotonic if its elements, from left to right, are entirely 
 * non-decreasing or entirely non-increasing.
 * 
 * Non-decreasing elements aren't necessarily strictly increasing; similarly, non-increasing 
 * elements aren't necessarily strictly decreasing.
 */
public class MonotonicArray {
    
    // O(n) time | O(1) space
    public static boolean isMonotonic(int[] array) {
        if (array.length <= 2) return true;
        
        int direction = array[1] - array[0];
        for (int i = 2; i < array.length; i++) {
            if (direction == 0) {
                direction = array[i] - array[i - 1];
                continue;
            }
            if (breaksDirection(direction, array[i - 1], array[i])) {
                return false;
            }
        }
        return true;
    }
    
    public static boolean breaksDirection(int direction, int previousInt, int currentInt) {
        int difference = currentInt - previousInt;
        if (direction > 0) return difference < 0;
        return difference > 0;
    }
    
    // Alternative approach
    public static boolean isMonotonicAlternative(int[] array) {
        boolean isNonDecreasing = true;
        boolean isNonIncreasing = true;
        for (int i = 1; i < array.length; i++) {
            if (array[i] < array[i - 1]) {
                isNonDecreasing = false;
            }
            if (array[i] > array[i - 1]) {
                isNonIncreasing = false;
            }
        }
        return isNonDecreasing || isNonIncreasing;
    }
    
    public static void main(String[] args) {
        int[] array1 = {-1, -5, -10, -1100, -1100, -1101, -1102, -9001};
        System.out.println(isMonotonic(array1)); // Expected: true
        
        int[] array2 = {1, 2, 0};
        System.out.println(isMonotonic(array2)); // Expected: false
        
        int[] array3 = {1, 1, 2, 3, 4, 5, 5, 5, 6, 7, 8, 8, 9, 10, 11};
        System.out.println(isMonotonic(array3)); // Expected: true
    }
}
