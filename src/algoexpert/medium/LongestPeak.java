package algoexpert.medium;

/**
 * Longest Peak
 * 
 * Write a function that takes in an array of integers and returns the length of the longest peak in the array.
 * 
 * A peak is defined as adjacent integers in the array that are strictly increasing until they reach a tip 
 * (the highest value in the peak), at which point they become strictly decreasing. At least three integers 
 * are required to form a peak.
 * 
 * For example, the integers 1, 4, 10, 2 form a peak, but the integers 4, 0, 10 don't and neither do the 
 * integers 1, 2, 2, 0. The first group of integers is strictly increasing before strictly decreasing; the 
 * second group of integers is not strictly increasing before the tip, and the third group of integers doesn't 
 * strictly decrease after the tip.
 */
public class LongestPeak {
    
    // O(n) time | O(1) space
    public static int longestPeak(int[] array) {
        int longestPeakLength = 0;
        int i = 1;
        while (i < array.length - 1) {
            boolean isPeak = array[i - 1] < array[i] && array[i] > array[i + 1];
            if (!isPeak) {
                i++;
                continue;
            }
            
            int leftIdx = i - 2;
            while (leftIdx >= 0 && array[leftIdx] < array[leftIdx + 1]) {
                leftIdx--;
            }
            
            int rightIdx = i + 2;
            while (rightIdx < array.length && array[rightIdx] < array[rightIdx - 1]) {
                rightIdx++;
            }
            
            int currentPeakLength = rightIdx - leftIdx - 1;
            longestPeakLength = Math.max(longestPeakLength, currentPeakLength);
            i = rightIdx;
        }
        return longestPeakLength;
    }
    
    public static void main(String[] args) {
        int[] array = {1, 2, 3, 3, 4, 0, 10, 6, 5, -1, -3, 2, 3};
        System.out.println(longestPeak(array)); // Expected: 6
        
        int[] array2 = {5, 4, 3, 2, 1, 2, 1};
        System.out.println(longestPeak(array2)); // Expected: 3
    }
}
