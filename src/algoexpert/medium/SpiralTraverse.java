package algoexpert.medium;

import java.util.*;

/**
 * Spiral Traverse
 * 
 * Write a function that takes in an n x m two-dimensional array (that can be square-shaped when n == m) 
 * and returns a one-dimensional array of all the array's elements in spiral order.
 * 
 * Spiral order starts at the top left corner of the two-dimensional array, goes to the right, 
 * and proceeds in a spiral pattern all the way until every element has been visited.
 */
public class SpiralTraverse {
    
    // O(n) time | O(n) space where n is the total number of elements in the array
    public static List<Integer> spiralTraverse(int[][] array) {
        if (array.length == 0) return new ArrayList<Integer>();
        
        List<Integer> result = new ArrayList<Integer>();
        int startRow = 0, endRow = array.length - 1;
        int startCol = 0, endCol = array[0].length - 1;
        
        while (startRow <= endRow && startCol <= endCol) {
            for (int col = startCol; col <= endCol; col++) {
                result.add(array[startRow][col]);
            }
            
            for (int row = startRow + 1; row <= endRow; row++) {
                result.add(array[row][endCol]);
            }
            
            for (int col = endCol - 1; col >= startCol; col--) {
                if (startRow == endRow) break;
                result.add(array[endRow][col]);
            }
            
            for (int row = endRow - 1; row >= startRow + 1; row--) {
                if (startCol == endCol) break;
                result.add(array[row][startCol]);
            }
            
            startRow++;
            endRow--;
            startCol++;
            endCol--;
        }
        
        return result;
    }
    
    public static void main(String[] args) {
        int[][] array = {
            {1, 2, 3, 4},
            {12, 13, 14, 5},
            {11, 16, 15, 6},
            {10, 9, 8, 7}
        };
        
        List<Integer> result = spiralTraverse(array);
        System.out.println(result); // Expected: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]
    }
}
