package meta;

public class RotaryLock {
    public static void main(String[] args) {
        System.out.println(getMinCodeEntryTime(3, 3, new int[]{1, 2, 3})); // Expected output: 2
        System.out.println(getMinCodeEntryTime(10, 4, new int[]{9, 4, 4, 8})); // Expected output: 11
        System.out.println(getMinCodeEntryTime(10, 4, new int[]{1, 1, 1, 1})); // Expected output: 0
        System.out.println(getMinCodeEntryTime(5, 2, new int[]{1, 3})); // Expected output: 0

        int[] C = new int[] {
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1,
                25000000, 1, 25000000, 1, 25000000, 1, 25000000, 1
        };

        System.out.println(getMinCodeEntryTime(50000000, 1000, C));
    }


    public static long getMinCodeEntryTime(int N, int M, int[] C) {
        // Validate inputs

        // Iterate over C
        int currentPosition = 1;
        long time = 0;
        for (int i = 0; i < M; i++) {
            int currentCode = C[i];

            int forwardTime = Math.abs(currentCode - currentPosition); // Each hop is 1 second
            int backwardTime = N - forwardTime;
            time += Math.min(forwardTime, backwardTime);

            currentPosition = currentCode;
        }
        return time;
    }

}
