package meta;

public class DirectorOfPhotography {

    public static void main(String[] args) {
        System.out.println(getArtisticPhotographCount(5, "APABA", 1, 2)); // Output: 1
        System.out.println(getArtisticPhotographCount(5, "APABA", 2, 3)); // Output: 0
        System.out.println(getArtisticPhotographCount(8, ".PBAAP.B", 1, 3)); // Output: 3
    }

    // Cubic time complexity
    public static int getArtisticPhotographCount(int N, String C, int X, int Y) {
        // Validate inputs

        int artisticPhotographs = 0;

        // Iterate over C and find photographers
        for (int i = 0; i < N; i++) {
            if (C.charAt(i) == 'P') {
                // For each photographer, we check if there is an actor within [X, Y] distance
                for (int j = i + X; j <= i + Y && j < N; j++) {
                    if (C.charAt(j) == 'A') {
                        // For each actor, we check if there is a backdrop within [X, Y] distance
                        for (int k = j + X; k <= j + Y && k < N; k++) {
                            if (C.charAt(k) == 'B') {
                               artisticPhotographs++;
                            }
                        }
                    }
                }

                for (int j = i - X; j >= i - Y && j >= 0; j--){
                    if (C.charAt(j) == 'A') {
                        for (int k = j - X; k >= j - Y && k >= 0; k--) {
                            if (C.charAt(k) == 'B') {
                                artisticPhotographs++;
                            }
                        }
                    }
                }
            }
        }

        return artisticPhotographs;
    }
}
