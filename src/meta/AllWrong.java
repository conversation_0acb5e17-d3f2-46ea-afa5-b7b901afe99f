package meta;

public class AllWrong {
    public static void main(String[] args) {
        String result = getWrongAnswers(3 , "ABA");
        System.out.println(result);

        result = getWrongAnswers(3 , "BBC");
        System.out.println(result);
    }

    private static String getWrongAnswers(int numberOfQuestions, String answers) {
        // Validate input
        if (numberOfQuestions != answers.length()) {
            // TODO: Create a specific excpetion instead of a generic runtime one
            throw new RuntimeException("Size of string " + answers + " must be " + numberOfQuestions);
        }
        // Other validations e.g. check that only A and/or B are present

        // For each character in input String
        // If the character is A replace it with B, if it's B replace it with A
        String result = "";
        for (int i = 0; i < numberOfQuestions ; i++) {
            char answer = answers.charAt(i);
            if (answer == 'A' ) {
                result += 'B';
            } else if (answer == 'B') {
                result += 'A';
            } else {
                throw new RuntimeException("Value " + answer + " not allowed");
            }
        }
        return result;
    }
}
