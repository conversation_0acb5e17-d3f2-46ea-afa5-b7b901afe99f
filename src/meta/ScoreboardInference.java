package meta;

public class ScoreboardInference {
    public static void main(String[] args) {

        int N1 = 6;
        int[] S1 = {1, 2, 3, 4, 5, 6};
        System.out.println(getMinProblemCount(N1, S1)); // 4

        int N2 = 4;
        int[] S2 = {4, 3, 3, 4};
        System.out.println(getMinProblemCount(N2, S2)); // 3

        int N3 = 4;
        int[] S3 = {2, 4, 6, 8};
        System.out.println(getMinProblemCount(N3, S3)); // 4
    }

    public static int getMinProblemCount(int N, int[] S) {
        boolean hasOdd = false;
        int minProblems = 0;

        for (int score : S) {
            if (score % 2 == 1) {
                hasOdd = true;
            }
            minProblems = Math.max(minProblems, score / 2);
        }

        if (hasOdd) {
            minProblems++; // Increment if there's any odd score to account for at least one 1-point problem
        }

        return minProblems;
    }
}
