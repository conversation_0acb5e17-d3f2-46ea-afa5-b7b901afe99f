package meta;

import java.util.Arrays;

public class Cafeteria {

    public static void main(String[] args) {
        long[] S = {2, 6};
        long result = getMaxAdditionalDinersCount(10, 1, 2, S);
        System.out.println(result); // Expected output: 3
    }

    public static long getMaxAdditionalDinersCount(long N, long K, int M, long[] S) {
        // Validate input e.g. S doesn't contain a number larger than N, and not more than M values, K isn't larger than N, the numbers in S are already separated by K or more...
        // Ensure S is sorted
        Arrays.sort(S);

        // Initialise a variable to store the result
        long additionalDiners = 0;
        long requiredSeatsPerDiners = K + 1; // The 1 is the seat for the diner, and K is the buffer

        // Calculate the potential additional seats before the first diner
        if (S[0] > 1) { // Unless the first diner is on the first seat
            long availableSeatsBeforeFirstDiner = S[0] - 1;
            additionalDiners += availableSeatsBeforeFirstDiner / requiredSeatsPerDiners;
        }

        // Calculate the potential additional seats between each pair of consecutive diners
        for (int i = 1; i < M ; i++) {
            long previous = S[i - 1];
            long current = S[i];
            long numberOfSeatsBetweenDiners = current - previous - 1;
            long buffer = 2 * K;
            long availableSeatsBetweenDiners = numberOfSeatsBetweenDiners - buffer;
            if (availableSeatsBetweenDiners > 0) {
                additionalDiners += (availableSeatsBetweenDiners + K) / requiredSeatsPerDiners;
            }
        }

        // Calculate the potential additional seats after the last diner
        if (S[M - 1] < N) { // If the last diner is not on the last seat
            long availableSeatsAfterLastDiner = N - S[M  - 1];
            additionalDiners += availableSeatsAfterLastDiner / requiredSeatsPerDiners;
        }

        return additionalDiners;
    }

}

