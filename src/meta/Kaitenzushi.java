package meta;

import java.util.HashSet;
import java.util.LinkedList;
import java.util.Queue;
import java.util.Set;

public class Kaitenzu<PERSON> {

    public static void main(String[] args) {
        System.out.println(getMaximumEatenDishCount(6, new int[]{1,2,3,3,2,1}, 1)); // Output: 5
        System.out.println(getMaximumEatenDishCount(6, new int[]{1,2,3,3,2,1}, 2)); // Output: 4
        System.out.println(getMaximumEatenDishCount(7, new int[]{1,2,1,2,1,2,1}, 2)); // Output: 2

        System.out.println(getMaximumEatenDishCount2(6, new int[]{1,2,3,3,2,1}, 1)); // Output: 5
        System.out.println(getMaximumEatenDishCount2(6, new int[]{1,2,3,3,2,1}, 2)); // Output: 4
        System.out.println(getMaximumEatenDishCount2(7, new int[]{1,2,1,2,1,2,1}, 2)); // Output: 2
    }

    // O (N X K)
    public static int getMaximumEatenDishCount(int N, int[] D, int K) {
        // Validate inputs

        // Iterate over the dishes D
        // For each new dish, we check if we've eaten a dish of the same type before
        // If yes, we skip, if not we eat and update to the collection while dropping the oldest one
        // We can solve this with a FIFO queue

        Queue queue = new LinkedList();
        int counter = 0;
        for (int i = 0; i < N; i++) {
            int currentDishType = D[i];
            if (!queue.contains(currentDishType)) {
                queue.add(currentDishType);
                counter++;
            }

            // Ensure that we only have K elements in the queue
            if (queue.size() > K) {
                queue.poll();
            }
        }

        return counter;
    }

    // O (1)
    public static int getMaximumEatenDishCount2(int N, int[] D, int K) {
        // Validate inputs

        // Same as before but now we keep track of the recent dishes in a HashSet to make the "contains" check more efficient

        Queue<Integer> queue = new LinkedList();
        Set<Integer> recent = new HashSet();
        int counter = 0;
        for (int i = 0; i < N; i++) {
            int currentDishType = D[i];
            if (!recent.contains(currentDishType)) {
                queue.add(currentDishType);
                recent.add(currentDishType);
                counter++;
            }

            // Ensure that we only have K elements in the queue
            if (queue.size() > K) {
                int oldestDish = queue.poll();
                recent.remove(oldestDish);
            }
        }

        return counter;
    }

}
