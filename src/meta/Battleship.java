package meta;

public class Battleship {

    public static void main(String[] args) {
        int[][] G = {
                {0, 0, 1},
                {1, 0, 1}
        };

        double result = getHitProbability(2, 3, G);
        System.out.println(result);

        int[][] G2 = {
                {1, 1},
                {1, 1}
        };

        result = getHitProbability(2, 2, G2);
        System.out.println(result);
    }

    private static double getHitProbability(int R, int C, int[][] G) {
        // Validate inputs e.g. R and C correspond the actual dimensions of G, G only contains 0s and 1s...

        // Iterate over each cell in the matrix, counting how many 1s and 0s
        // Divide number of 1s by the total number of positions
        double oneCounter = 0D;
        double zeroCounter = 0D;

        for (int i = 0 ; i < R ; i++) {
            for (int j = 0; j < C; j++) {
                if (G[i][j] == 1) {
                    oneCounter++;
                } else {
                    zeroCounter++;
                }
            }
        }

        return oneCounter / (oneCounter + zeroCounter);
    }
}
