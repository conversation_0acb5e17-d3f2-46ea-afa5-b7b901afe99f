package meta;

public class UniformIntegers {
    public static void main(String[] args) {
        System.out.println(getUniformIntegerCountInInterval2(75, 300)); // Expected output: 5
        System.out.println(getUniformIntegerCountInInterval2(1, 9)); // Expected output: 9
        System.out.println(getUniformIntegerCountInInterval2(Long.parseLong("999999999999"), Long.parseLong("999999999999"))); // Expected output: 1
    }

    public static int getUniformIntegerCountInInterval(long A, long B) {
        int uniformIntegersCount = 0;

        for (long i = A; i <= B; i++) {
            // Iterate over the digits of i and check if they are all the same
            String iString = String.valueOf(i);
            boolean isUniform = true;
            for (char digit : iString.toCharArray()) {
                if (digit != iString.charAt(0)) {
                    isUniform = false;
                    break;
                }
            }
            if (isUniform) {
                uniformIntegersCount++;
            }
        }

        return uniformIntegersCount;
    }


    public static int getUniformIntegerCountInInterval2(long A, long B) {
        int uniformIntegersCount = 0;

        // Generate all uniform integers and check if they are in the interval
        for (int digit = 1; digit <= 9; digit++) {
            long uniformInteger = digit;
            while (uniformInteger <= B) {
                if (uniformInteger >= A) {
                    uniformIntegersCount++;
                }
                uniformInteger = uniformInteger * 10 + digit;
                //System.out.println(uniformInteger);
            }
        }

        return uniformIntegersCount;
    }
}
