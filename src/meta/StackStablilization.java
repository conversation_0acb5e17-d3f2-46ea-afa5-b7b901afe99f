package meta;

import java.util.HashSet;

public class StackStablilization {

    public static void main(String[] args) {
        System.out.println(getMinimumDeflatedDiscCount2(5, new int[]{2, 5, 3, 6, 5})); // Expected output: 3
        System.out.println(getMinimumDeflatedDiscCount2(3, new int[]{100, 100, 100})); // Expected output: 2
        System.out.println(getMinimumDeflatedDiscCount2(4, new int[]{6, 5, 4, 3})); // Expected output: -1
    }

    // O(N^2) time and O(N) space
    public static int getMinimumDeflatedDiscCount(int N, int[] R) {
        int requiredChangesCount = 0;
        int currentValue = 0;
        int nextValue = 0;
        HashSet<Integer> deflatedDisks = new HashSet<>();
        for (int i = 0; i < N - 1; i++) {
            //System.out.println(Arrays.toString(R));
            currentValue = R[i];
            nextValue = R[i + 1];
            if (currentValue >= nextValue) { // We need to deflate the current disc
                if (nextValue == 1) {
                    return -1; // We can't deflate the next disc
                }
                if (!deflatedDisks.contains(i)){ // We haven't deflated this disk before
                    requiredChangesCount++;
                }
                // Deflate the current disk
                R[i] = nextValue - 1;
                deflatedDisks.add(i);

                // Restart the loop
                if (i < N - 1 ) {
                    i = -1;
                }
            }
        }
        return requiredChangesCount;
    }

    // O(N) time and O(1) space
    public static int getMinimumDeflatedDiscCount2(int N, int[] R) {
        int requiredChangesCount = 0;
        // Start from the second last disk
        for (int i = N - 2; i >= 0; i--) {
            // If the current disk is larger than the next disk
            if (R[i] >= R[i + 1]) {
                // Check if we can deflate the next disk
                if (R[i + 1] == 1) {
                    return -1;
                }
                // Deflate the current disk
                R[i] = R[i + 1] - 1;
                requiredChangesCount++;
            }
        }
        return requiredChangesCount;
    }
}
